import React, { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import store from '@/redux/store';
import { ToastProvider } from '@/hocs/toast';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';
import withThemeProvider from '@/hocs/withTheme';
import RootNavigator from '@/navigation/RootNavigator';
import '@/i18n/i18n';
import { LoadingProvider } from '@/hocs/LoadingContext';
import Loading from '@/components/Loading';
import { checkAndHandleNewInstall } from '@/utils/authUtils';
import { initializeApiClient } from '@/services/api/apiClient';

// Create themed root component
const ThemedRoot = withThemeProvider(() => {
  // Create App without permission logs to prevent loops
  return (
    <SafeAreaProvider>
      <ToastProvider>
        <LoadingProvider>
          <RootNavigator />
          <Loading />
        </LoadingProvider>
      </ToastProvider>
    </SafeAreaProvider>
  );
});

function App(): React.JSX.Element {
  useEffect(() => {
    const initializeApp = async () => {
      // Initialize API client with saved URL
      await initializeApiClient();

      // Check for new install
      checkAndHandleNewInstall();
    };

    initializeApp();
  }, []);

  return (
    <Provider store={store}>
      <GestureHandlerRootView style={BASE_STYLE.flex}>
        <ThemedRoot />
      </GestureHandlerRootView>
    </Provider>
  );
}

export default App;