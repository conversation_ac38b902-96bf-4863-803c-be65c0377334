import Config from 'react-native-config';

export const AUTH_TOKEN_KEY = 'access_token';
export const REFRESH_TOKEN_KEY = 'refresh_token';
export const SESSION_ID_KEY = 'session_id';
export const BASE_URL_KEY = 'base_url';
export const API_TIMEOUT = 15000; // 30 seconds timeout
export const DEFAULT_BASE_URL = Config.API_URL;

export const API_VERSION = '/v1';
export const ENDPOINTS = {
    user: {
        me: API_VERSION + '/user/me',
        changePassword: API_VERSION + '/user/change-password'
    },
    auth: {
        login: API_VERSION + '/auth/login',
        logout: API_VERSION + '/auth/logout',
        forgotPassword: API_VERSION + '/auth/password/forgot'
    },
};
