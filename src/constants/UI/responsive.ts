import { Dimensions, PixelRatio, useWindowDimensions, Platform } from 'react-native';
import {
    FONT_SIZE,
    ICON_SIZE,
    HEADER_HEIGHT,
    DEVICES_MAXWIDTH,
    DEVICES_BREAKPOINTS,
    DESIGN_BASE_WIDTH,
    DEVICE_OPTION,
} from './themes';
import { useMemo } from 'react';
import { DeviceType, FontSizeKeys } from '@/types';

const defaultDimensions = Dimensions.get('window');
export const defaultWidth = defaultDimensions.width;
export const defaultHeight = defaultDimensions.height;
const fontScale = PixelRatio.getFontScale();

export const isAndroid = Platform.OS === 'android';
export const isIOS = Platform.OS === 'ios';

const SCREEN_SCALE_FACTOR = {
    SMALL: 0.85,
    MEDIUM: 0.95,
    LARGE: 1,
};

export const getScreenBasedScale = (width: number = defaultWidth): number => {
    if (width <= DEVICES_BREAKPOINTS.mobile) {
        return SCREEN_SCALE_FACTOR.SMALL;
    } else if (width <= DESIGN_BASE_WIDTH) {
        return SCREEN_SCALE_FACTOR.MEDIUM;
    }
    return SCREEN_SCALE_FACTOR.LARGE;
};


// Utility functions with platform-specific adjustments
export const scale = (size: number, width: number = defaultWidth): number => (width / DESIGN_BASE_WIDTH) * size;
export const pixelSize = (size: number): number => Math.round(PixelRatio.roundToNearestPixel(size / fontScale));

// Scale font size with platform-specific adjustments
export const scaleFontSize = (size: number): number => {
    const scaleFactor = getScreenBasedScale();

    if (isAndroid) {
        return pixelSize(size * scaleFactor);
    }
    return pixelSize(size);
};

export const adjustHeight = (height: number): number => {
    const { width } = Dimensions.get('window');
    const screenScale = getScreenBasedScale(width);

    if (isAndroid) {
        return height * screenScale;
    }
    return height;
};

export const adjustWidth = (width: number): number => {
    const { width: screenWidth } = Dimensions.get('window');
    const screenScale = getScreenBasedScale(screenWidth);

    if (isAndroid) {
        return width * screenScale;
    }
    return width;
};

// Adjust spacing/margins for platform differences
export const adjustSpacing = (spacing: number): number => {
    if (isAndroid && fontScale > 1) {
        return Math.round(spacing * 0.85);
    }
    return spacing;
};

export const getBreakpoint = (width: number = defaultWidth): DeviceType => {
    if (width >= DEVICES_BREAKPOINTS.desktop) return DEVICE_OPTION.desktop;
    if (width >= DEVICES_BREAKPOINTS.tablet) return DEVICE_OPTION.tablet;
    return DEVICE_OPTION.mobile;
};

export const isLandscape = (width: number = defaultWidth, height: number = defaultHeight): boolean => width > height;

// Responsive font sizes with platform-specific adjustments
export const RESPONSIVE_FONT_SIZE: Record<FontSizeKeys, Record<DeviceType, number>> = {
    xs: {
        mobile: scaleFontSize(FONT_SIZE.xs),
        tablet: scaleFontSize(FONT_SIZE.xs * 1.1),
        desktop: scaleFontSize(FONT_SIZE.xs * 1.2),
    },
    sm: {
        mobile: scaleFontSize(FONT_SIZE.sm),
        tablet: scaleFontSize(FONT_SIZE.sm * 1.1),
        desktop: scaleFontSize(FONT_SIZE.sm * 1.2),
    },
    md: {
        mobile: scaleFontSize(FONT_SIZE.md),
        tablet: scaleFontSize(FONT_SIZE.md * 1.1),
        desktop: scaleFontSize(FONT_SIZE.md * 1.2),
    },
    lg: {
        mobile: scaleFontSize(FONT_SIZE.lg),
        tablet: scaleFontSize(FONT_SIZE.lg * 1.1),
        desktop: scaleFontSize(FONT_SIZE.lg * 1.2),
    },
    xl: {
        mobile: scaleFontSize(FONT_SIZE.xl),
        tablet: scaleFontSize(FONT_SIZE.xl * 1.1),
        desktop: scaleFontSize(FONT_SIZE.xl * 1.2),
    },
    xxl: {
        mobile: scaleFontSize(FONT_SIZE.xxl),
        tablet: scaleFontSize(FONT_SIZE.xxl * 1.1),
        desktop: scaleFontSize(FONT_SIZE.xxl * 1.2),
    },
    xxxl: {
        mobile: scaleFontSize(FONT_SIZE.xxxl),
        tablet: scaleFontSize(FONT_SIZE.xxxl * 1.1),
        desktop: scaleFontSize(FONT_SIZE.xxxl * 1.2),
    },
    x4l: {
        mobile: scaleFontSize(FONT_SIZE.x4l),
        tablet: scaleFontSize(FONT_SIZE.x4l * 1.1),
        desktop: scaleFontSize(FONT_SIZE.x4l * 1.2),
    },
    x5l: {
        mobile: scaleFontSize(FONT_SIZE.x5l),
        tablet: scaleFontSize(FONT_SIZE.x5l * 1.1),
        desktop: scaleFontSize(FONT_SIZE.x5l * 1.2),
    },
    x6l: {
        mobile: scaleFontSize(FONT_SIZE.x6l),
        tablet: scaleFontSize(FONT_SIZE.x6l * 1.1),
        desktop: scaleFontSize(FONT_SIZE.x6l * 1.2),
    },
};

// Responsive icon size
export const RESPONSIVE_ICON_SIZE: Record<DeviceType, number> = {
    mobile: pixelSize(ICON_SIZE),
    tablet: pixelSize(ICON_SIZE * 1.2),
    desktop: pixelSize(ICON_SIZE * 1.4),
};

// Responsive header height
export const RESPONSIVE_HEADER_HEIGHT: Record<DeviceType, number> = {
    mobile: adjustHeight(scale(HEADER_HEIGHT)),
    tablet: adjustHeight(scale(HEADER_HEIGHT * 1.1)),
    desktop: adjustHeight(scale(HEADER_HEIGHT * 1.2)),
};

// Grid system
export const GRID = (width: number = defaultWidth) => ({
    columns: 12,
    maxWidth: DEVICES_MAXWIDTH
});

// Hook to provide responsive utilities
export const useResponsiveUtils = () => {
    const { width, height } = useWindowDimensions();
    const breakpoint = getBreakpoint(width);
    const landscape = isLandscape(width, height);

    return useMemo(() => {
        return {
            scale,
            pixelSize,
            scaleFontSize,
            adjustHeight,
            adjustSpacing,
            getBreakpoint,
            isLandscape,
            width,
            height,
            breakpoint,
            landscape,
            fontScale,
            isAndroid,
            isIOS,
            getScreenBasedScale
        }
    }, [width, height, breakpoint, landscape]);
};

// Simplified utility function to get responsive font size
export const getResponsiveFontSize = (
    size: number,
    options?: {
        reducedForAndroid?: boolean,
        increasedForTablet?: boolean
    }
): number => {
    const scaleFactor = getScreenBasedScale();
    const breakpoint = getBreakpoint();

    let adjustedSize = size;

    // Apply platform-specific adjustment
    if (isAndroid && options?.reducedForAndroid !== false) {
        adjustedSize *= scaleFactor;
    }

    // Apply device type adjustment
    if (options?.increasedForTablet !== false) {
        if (breakpoint === 'tablet') {
            adjustedSize *= 1.1;
        } else if (breakpoint === 'desktop') {
            adjustedSize *= 1.2;
        }
    }

    return pixelSize(adjustedSize);
};