import {
    Colors,
    DeviceType,
    Orientation,
    StatusBar,
    ThemeOptions,
    SpacingKeys,
    BorderRadiusKeys,
    FontWeightKeys,
    ShadowStyle,
    ShadowKeys,
    OpacityKeys,
    Animations,
    FontFamilyKeys,
    PaddingKeys,
    InsetKeys,
    LayoutKeys,
    ZIndexKeys,
    AppTheme
} from "@/types";
import { getResponsiveFontSize } from '@/constants/UI/responsive';

export const COLORS: Colors = {
    light: {
        primary: '#00B9A1',
        secondary: '#50E3C2',
        backgroundGradient: ['#CDFFF8', '#DEEDEB'],
        background: '#F5F7FA',
        white: '#FFFFFF',
        card: '#FFFFFF',
        black: '#000000',
        text: '#2A2A2A',
        textSecondary: '#716e6e',
        error: '#FE5F5F',
        success: '#17C964',
        warning: '#FEE95F',
        info: '#5AC8FA',
        border: '#E5E5EA',
        disabled: '#C7C7CC',
        blurPopup: '#757171',
        linearGradient: ['#F2FFFD', '#DEEDEB'],
        notification: '#FFFFFF',
        surface: '#FFFFFF',
        onPrimary: '#FFFFFF',
        placeholder: '#838181',
        price: "#498291",
        gray: '#555555',
        green: '#f0fefe',
        avatarBorder: '#AEDBD5',
        avatarBackground: '#BBE9E2',
    },

    dark: {
        primary: '#2BE3C7',
        secondary: '#72FFE3',
        backgroundGradient: ['#3A8A7D', '#5A6B67'],
        background: '#566c6f',
        white: '#E5E5EA',
        card: '#FFFFFF',
        black: '#000000',
        text: '#E5E5EA',
        textSecondary: '#A0A0A0',
        error: '#FE5F5F',
        success: '#17C964',
        warning: '#FEE95F',
        info: '#5AC8FA',
        border: '#404040',
        disabled: '#606060',
        blurPopup: '#757171',
        linearGradient: ['#F2FFFD', '#DEEDEB'],
        notification: '#FFFFFF',
        surface: '#404040',
        onPrimary: '#FFFFFF',
        placeholder: '#A0A0A0',
        price: "#498291",
        gray: '#555555',
        green: '#f0fefe',
        avatarBorder: '#AEDBD5',
        avatarBackground: '#BBE9E2',
    },
};

export const GRADIENT_VALUE = [0.3, 0.7];
export const INPUT_HEIGHT = 48;
export const BUTTON_HEIGHT = 48;
export const LINE_HEIGHT = 16;
export const BORDER_WIDTH = 1;
export const SCALE = 0.9;
export const FONT_SIZE = {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 30,
    x4l: 36,
    x5l: 42,
    x6l: 50,
    size18: 18,
    size20: 20,
    size22: 22,
    size26: 26,
    size40: 40,
    size64: 64
} as const;

type FontSizeKeysType = keyof typeof FONT_SIZE;
export { type FontSizeKeysType as FontSizeKeys };

export const SPACING: Record<SpacingKeys, number> = {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 40,
    size12: 12,
    size14: 14,
    size18: 18,
    size20: 20,
    size30: 30,
    size48: 48,
};

export const BORDER_RADIUS: Record<BorderRadiusKeys, number> = {
    xs: 4,
    xsm: 6,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    round: 9999,
};


export const FONT_WEIGHT: Record<FontWeightKeys, '300' | '400' | '500' | '600' | '700' | '800'> = {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
    extraBold: '800',
};

export const SHADOWS: Record<ShadowKeys, ShadowStyle> = {
    small: {
        elevation: 2,
        shadowColor: '#000000',
        shadowOpacity: 0.1,
        shadowRadius: 4,
        shadowOffset: { width: 0, height: 2 },
    },
    medium: {
        elevation: 4,
        shadowColor: '#000000',
        shadowOpacity: 0.15,
        shadowRadius: 6,
        shadowOffset: { width: 0, height: 4 },
    },
    large: {
        elevation: 8,
        shadowColor: '#000000',
        shadowOpacity: 0.2,
        shadowRadius: 10,
        shadowOffset: { width: 0, height: 6 },
    },
};

export const OPACITY: Record<OpacityKeys, number> = {
    disabled: 0.5,
    pressed: 0.7,
    overlay: 0.4,
};

export const ANIMATIONS: Animations = {
    duration: {
        short: 200,
        medium: 400,
        long: 600,
    },
    easing: {
        standard: 'ease-in-out',
        decelerate: 'ease-out',
        accelerate: 'ease-in',
    },
};

export const ZINDEX: Record<ZIndexKeys, number> = {
    base: 0,
    elevated: 10,
    dropdown: 50,
    modal: 100,
    loading: 150,
    tooltip: 200,
    toast: 300,
    weekDays: 400,
    overlay: 998,
    calendar: 999,
};

export const FONT_FAMILY: Record<FontFamilyKeys, string> = {
    thin: 'Lato',
    thinItalic: 'Lato-ThinItalic',
    light: 'Lato-Light',
    lightItalic: 'Lato-LightItalic',
    regular: 'Lato-Regular',
    italic: 'Lato-Italic',
    bold: 'Lato-Bold',
    boldItalic: 'Lato-BoldItalic',
    black: 'Lato-Black',
    blackItalic: 'Lato-BlackItalic',
};

export const PADDING: Record<PaddingKeys, number> = {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 40,
};

export const FONTS: AppTheme['fonts'] = {
    bodySmall: {
        fontSize: FONT_SIZE.sm,
        fontFamily: FONT_FAMILY.regular
    },
    bodyMedium: {
        fontSize: FONT_SIZE.md,
        fontFamily: FONT_FAMILY.regular
    },
    bodyLarge: {
        fontSize: FONT_SIZE.lg,
        fontFamily: FONT_FAMILY.regular
    },
    titleMedium: {
        fontSize: FONT_SIZE.xl,
        fontFamily: FONT_FAMILY.bold
    }
};

export const DEVICE_OPTION: Record<DeviceType, DeviceType> = {
    mobile: 'mobile',
    tablet: 'tablet',
    desktop: 'desktop',
};

export const DEVICES_BREAKPOINTS: Record<DeviceType, number> = {
    mobile: 320,
    tablet: 768,
    desktop: 1024,
};

export const DEVICES_MAXWIDTH: Record<DeviceType, number | string> = {
    mobile: '100%',
    tablet: 780,
    desktop: 960,
};

export const HEADER_HEIGHT = 107;

export const ICON_SIZE = 40;

export const DESIGN_BASE_WIDTH = 375;

export const THEME = {
    LIGHT: 'light' as ThemeOptions,
    DARK: 'dark' as ThemeOptions,
} as const;

export const STATUS_BAR = {
    LIGHT: 'light-content' as StatusBar,
    DARK: 'dark-content' as StatusBar
};

export const CONST_ORIENTATION: Record<Orientation, Orientation> = {
    portrait: 'portrait',
    landscape: 'landscape',
};

export const INSETS: Record<InsetKeys, Record<'ios' | 'android', number>> = {
    keyboard: {
        ios: 100,
        android: 40,
    },
    navigation: {
        ios: 0,
        android: 16,
    },
    input: {
        ios: 50,
        android: 80,
    },
    content: {
        ios: 16,
        android: 24,
    },
    button: {
        ios: 10,
        android: 10,
    },
};

export const LAYOUT: Record<LayoutKeys, {
    portrait: number;
    landscape: number;
}> = {
    containerPadding: {
        portrait: 16,
        landscape: 24,
    },
    contentSpacing: {
        portrait: 16,
        landscape: 24,
    },
};

export const DefaultTheme: AppTheme = {
    dark: false,
    colors: COLORS.light,
    fonts: FONTS,
    sizes: {
        borderRadius: {
            small: BORDER_RADIUS.xs,
            medium: BORDER_RADIUS.sm,
            large: BORDER_RADIUS.md
        },
        inputHeight: INPUT_HEIGHT,
        buttonHeight: BUTTON_HEIGHT,
        font: {
            small: FONT_SIZE.sm,
            medium: FONT_SIZE.md,
            large: FONT_SIZE.lg
        }
    },
    spacing: {
        xs: SPACING.xs,
        sm: SPACING.sm,
        md: SPACING.md,
        lg: SPACING.lg,
        xl: SPACING.xl
    },
    default: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
    },
    linearGradient: COLORS.light.linearGradient
};

export const DarkTheme: AppTheme = {
    ...DefaultTheme,
    dark: true,
    colors: COLORS.dark,
};
