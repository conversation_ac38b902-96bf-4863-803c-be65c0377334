import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { AppTheme } from '@/types';
const { colors } = useTheme() as unknown as AppTheme;

interface NotificationItemProps {
    title: string;
    date: string;
    desc: string;
    unread: boolean;
    isLast?: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
    title,
    date,
    desc,
    unread,
    isLast,
}) => (
    <View>
        <View style={styles.row}>
            <Text style={styles.title}>{title}</Text>
            <View style={styles.rightCol}>
                <Text style={styles.date}>{date}</Text>
                {unread && <View style={styles.dot} />}
            </View>
        </View>
        <Text style={styles.desc}>{desc}</Text>
        {!isLast && <View style={styles.divider} />}
    </View>
);

const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    rightCol: {
        flexDirection: 'column',
        alignItems: 'center',
        marginLeft: 8,
        gap: 2,
    },
    title: {
        fontWeight: 'bold',
        fontSize: 17,
        flex: 1,
        color: colors.text,
        marginRight: 10,
    },
    date: {
        fontSize: 16,
        color: colors.text,
        marginRight: 4,
        fontWeight: '500',
    },
    dot: {
        width: 10,
        height: 10,
        borderRadius: 50,
        backgroundColor: '#FE5F5F',
        marginLeft: 2,
        marginTop: 3,
        alignSelf: 'center',
    },
    desc: {
        fontSize: 15,
        color: colors.text,
        marginTop: 8,
        marginBottom: 6,
        marginLeft: 2,
    },
    divider: {
        height: 1,
        backgroundColor: colors.primary,
        marginVertical: 8,
        borderRadius: 1,
        opacity: 0.35,
    },
});

export default NotificationItem;