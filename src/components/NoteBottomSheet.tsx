import React, { useState, useRef, useEffect } from "react";
import { View, Text, FlatList, TextInput, TouchableOpacity, StyleSheet, Modal, Platform, KeyboardAvoidingView, Keyboard, StatusBar, Dimensions } from "react-native";
import { usersMockData } from "@/constants/mock/users";
import { format } from "date-fns";
import CIcon from "@/components/CIcon";
import { Iclose } from "@/constants/UI/icons";
import { adjustHeight, adjustSpacing, getResponsiveFontSize, isAndroid } from "@/constants/UI/responsive";
import { COLORS, FONT_WEIGHT, INSETS } from "@/constants/UI/themes";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ichange, Itrash } from "@/constants/UI/icons";
import ProfileAvatar from "./ProfileAvatar";
import CText from "./CText";

interface Note {
  id: string;
  userId: string;
  content: string;
  createdAt: string;
}

interface NoteBottomSheetProps {
  title: string;
  notes: Note[];
  onAdd: (content: string) => void;
  onEdit: (id: string, content: string) => void;
  onDelete: (id: string) => void;
  currentUserId: string;
  onClose: () => void;
}

const NoteBottomSheet: React.FC<NoteBottomSheetProps> = ({
  title,
  notes = [{ id: "1", userId: "1", content: "test", createdAt: "2021-01-01" }],
  onAdd,
  onEdit,
  onDelete,
  currentUserId,
  onClose,
}) => {
  const [input, setInput] = useState("");
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState("");
  const [menuNoteId, setMenuNoteId] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const insets = useSafeAreaInsets();
  const isAndroid = Platform.OS === 'android';
  const ANDROID_KEYBOARD_OFFSET = StatusBar.currentHeight ? StatusBar.currentHeight + 80 : 50;

  // Add refs for both input fields
  const addInputRef = useRef<TextInput>(null);
  const editInputRef = useRef<TextInput>(null);

  // Effect to handle keyboard dismiss when component unmounts
  useEffect(() => {
    return () => {
      Keyboard.dismiss();
    };
  }, []);

  const handleAdd = () => {
    if (input.trim()) {
      onAdd(input.trim());
      setInput("");
    }
  };

  const handleEdit = () => {
    if (editingId && editingContent.trim()) {
      onEdit(editingId, editingContent.trim());
      setEditingId(null);
      setEditingContent("");
    }
  };

  const handleMenuPress = (event: any, noteId: string) => {
    const { pageX, pageY } = event.nativeEvent;
    setMenuPosition({ x: pageX - 100, y: pageY - 20 });
    setMenuNoteId(noteId);
  };

  // Modify the edit menu item press handler
  const handleEditPress = (item: Note) => {
    setEditingId(item.id);
    setEditingContent(item.content);
    setMenuNoteId(null);
    // Focus the edit input after a short delay to ensure the state has updated
    setTimeout(() => {
      editInputRef.current?.focus();
    }, 100);
  };

  const renderNote = ({ item }: { item: Note }) => {
    const user = usersMockData.find((u) => String(u.id) === item.userId);
    const isCurrentUser = String(item.userId) === String(currentUserId);

    return (
      <View style={styles.noteRow}>

        <ProfileAvatar name={'Unikey Kia'} style={styles.avatarCircle} />
        <View style={{ flex: 1 }}>
          <View style={styles.noteHeader}>

            <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "space-between" }}>
              <CText style={styles.userName}>{user?.name || "Unknown"}</CText>
            </View>

            {isCurrentUser && (
              <TouchableOpacity
                onPress={(e) => handleMenuPress(e, item.id)}
                style={styles.menuButton}
              >
                <Text style={styles.noteDate}>
                  {format(new Date(item.createdAt), "dd MMM h:mm a")}
                </Text>
                <Text style={styles.menuDots}>⋮</Text>
              </TouchableOpacity>
            )}
          </View>
          <CText style={styles.noteContent}>{item.content}</CText>
        </View>


        {/* Menu Modal */}
        <Modal
          visible={menuNoteId === item.id}
          transparent
          animationType="fade"
          onRequestClose={() => setMenuNoteId(null)}
        >
          <TouchableOpacity
            style={styles.menuOverlay}
            onPress={() => setMenuNoteId(null)}
          />
          <View style={[
            styles.menuModal,
            {
              position: 'absolute',
              left: menuPosition.x - 20,
              top: menuPosition.y,
            }
          ]}>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                handleEditPress(item);
              }}
            >
              <CIcon source={Ichange} size={24} tintColor={COLORS.light.primary} />
              <CText style={styles.menuItemText}>Edit Note</CText>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                onDelete(item.id);
                setMenuNoteId(null);
              }}
            >
              <CIcon source={Itrash} size={24} tintColor={COLORS.light.error} />

              <CText style={[styles.menuItemText]}>
                Delete Note
              </CText>
            </TouchableOpacity>
          </View>
        </Modal>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={[styles.container, { paddingBottom: isAndroid ? insets.bottom : 0 }]}
      keyboardVerticalOffset={Platform.OS === "ios" ? adjustHeight(120) : adjustHeight(ANDROID_KEYBOARD_OFFSET)}
    >
      <View style={styles.header}>
        <View style={styles.headerContainer}>
          <CText style={[styles.title, { alignSelf: "center" }]}>{title}</CText>
          <TouchableOpacity onPress={() => {
            onClose();
            Keyboard.dismiss();
          }} style={styles.headerCloseButton}>
            <CIcon source={Iclose} size={24} />
          </TouchableOpacity>
        </View>
      </View>

      <FlatList
        data={notes as any[]}
        renderItem={renderNote}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        style={styles.flatList}
        contentContainerStyle={styles.listContent}
        keyboardShouldPersistTaps="handled"
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No notes found.</Text>
          </View>
        )}
      />

      {editingId ? (
        <View style={styles.inputRow}>
          <TextInput
            ref={editInputRef}
            style={styles.input}
            value={editingContent}
            onChangeText={setEditingContent}
            placeholder="Edit note..."
            multiline
            numberOfLines={7}
          />
          <TouchableOpacity style={styles.sendButton} onPress={handleEdit}>
            <Text style={styles.sendButtonText}>✓</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.inputRow}>
          <TextInput
            ref={addInputRef}
            style={styles.input}
            value={input}
            onChangeText={setInput}
            placeholder="Add a note..."
            multiline
            numberOfLines={7}
          />
          <TouchableOpacity style={styles.sendButton} onPress={handleAdd}>
            <Text style={styles.sendButtonText}>✓</Text>
          </TouchableOpacity>
        </View>
      )}
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: "relative",
    paddingBottom: isAndroid ? 100 : 90,
  },
  handleBar: {
    alignSelf: "center",
    width: 48,
    height: 5,
    borderRadius: 3,
    backgroundColor: "#E0E0E0",
    marginTop: 8,
    marginBottom: 8,
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    height: 60,
    marginTop: -20,
  },
  headerCloseButton: {
    position: "absolute",
    right: 16,
    top: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  title: {
    fontSize: getResponsiveFontSize(18),
    fontWeight: "bold",
  },
  noteRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
    paddingBottom: adjustSpacing(18),
    borderBottomWidth: 1,
    borderColor: COLORS.light.primary,
  },
  avatarCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#e0f2f1",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  avatarText: {
    fontWeight: FONT_WEIGHT.medium,
    color: "#009688",
    fontSize: getResponsiveFontSize(16),
  },
  noteHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 2,
    justifyContent: "space-between"
  },
  userName: {
    fontWeight: FONT_WEIGHT.medium,
    marginRight: 8,
    fontSize: getResponsiveFontSize(16),
  },
  noteDate: {
    color: "#888",
    fontSize: getResponsiveFontSize(16),
    marginRight: 8,
  },
  menuButton: {
    padding: 4,
    flexDirection: "row",
    alignItems: "center",
  },
  menuDots: {
    fontSize: getResponsiveFontSize(20),
    color: "#888",
  },
  noteContent: {
    fontSize: getResponsiveFontSize(18),
    color: "#222",
    marginBottom: 2,
  },
  inputRow: {
    flexDirection: "row",
    alignItems: "flex-end",
    borderWidth: 1,
    borderColor: "#b2dfdb",
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
    minHeight: 60,
  },
  input: {
    flex: 1,
    minHeight: 36,
    maxHeight: 100,
    fontSize: getResponsiveFontSize(15),
    padding: 0,
    marginRight: 8,
  },
  sendButton: {
    backgroundColor: "#009688",
    borderRadius: 20,
    width: 36,
    height: 36,
    alignItems: "center",
    justifyContent: "center",
  },
  sendButtonText: {
    color: "#fff",
    fontSize: getResponsiveFontSize(20),
    fontWeight: "bold",
  },
  menuOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.1)",
  },
  menuModal: {
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    paddingVertical: 8,
    paddingHorizontal: 12,
    minWidth: 150,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: adjustSpacing(4),
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginLeft: adjustSpacing(-10)
  },
  menuItemText: {
    fontSize: getResponsiveFontSize(16),
  },
  flatList: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: isAndroid ? 16 : 0,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    color: "#888",
    fontSize: getResponsiveFontSize(16),
  },
});

export default NoteBottomSheet;
