// src/components/ImagePicker/PhotoLibraryModal.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Image,
    FlatList,
    Modal,
    SafeAreaView,
    Alert,
    Platform
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import { FONT_WEIGHT } from '@/constants/UI/themes';
import RNFS from 'react-native-fs';
import { isImageDuplicate } from '@/utils/imageUtils';

interface PhotoLibraryModalProps {
    visible: boolean;
    onClose: () => void;
    onSelect: (selectedImages: any[]) => void;
    multiple?: boolean;
    maxImages?: number;
    photosTaken?: any[];
}

interface GalleryImage {
    uri: string;
    selected: boolean;
    isFromApp: boolean;
}

const PhotoLibraryModal: React.FC<PhotoLibraryModalProps> = ({
    visible,
    onClose,
    onSelect,
    multiple = false,
    maxImages = 10,
    photosTaken = []
}) => {
    const { colors } = useTheme() as any;
    const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);


    // Load photos when modal becomes visible
    useEffect(() => {
        if (visible) {
            loadPhotos();
        }
    }, [visible]);

    const loadAppCachePhotos = async () => {
        try {
            const tmpPath = RNFS.TemporaryDirectoryPath;
            const files = await RNFS.readDir(tmpPath);

            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const imageFiles = files.filter(file => {
                const mtime = file.mtime ? new Date(file.mtime) : null;
                const isToday = mtime && mtime >= today;
                const isFromApp = file.name.startsWith('ARC_')
                return isFromApp && isToday;
            });

            const safePhotosTaken = photosTaken ?? [];
            const formattedPhotos = imageFiles
                .map(file => {
                    const isSelected = safePhotosTaken?.some(photo =>
                        photo.uri === `file://${file.path}` ||
                        photo.filename === file.name
                    );

                    return {
                        uri: `file://${file.path}`,
                        filename: file.name,
                        timestamp: file.mtime?.getTime() || Date.now(),
                        selected: isSelected,
                        isFromApp: true,
                    };
                })
                .sort((a, b) => b.timestamp - a.timestamp)
                .slice(0, 99);

            setGalleryImages(formattedPhotos);
        } catch (error) {
            console.error('Error loading app cache photos:', error);
            Alert.alert('Error', 'Unable to load photos from app cache');
        }
    };


    const loadPhotos = async () => {
        try {
            await loadAppCachePhotos();

        } catch (error) {
            console.error('Error loading photos:', error);
            Alert.alert('Error', 'Unable to load photos');
            onClose();
        }
    };


    const toggleImageSelection = useCallback((index: number) => {
        setGalleryImages(prev => {
            const updatedImages = [...prev];

            if (!multiple) {
                // If not selecting multiple images, deselect all other images
                updatedImages.forEach((img, i) => {
                    updatedImages[i].selected = i === index;
                });
            } else {
                // If selecting multiple images
                const selectedCount = updatedImages.filter(img => img.selected).length;

                // If image is already selected, deselect it
                if (updatedImages[index].selected) {
                    updatedImages[index].selected = false;
                }
                // If image is not selected and haven't reached the limit, select it
                else if (selectedCount < maxImages) {
                    updatedImages[index].selected = true;
                }
                // If reached the limit, show notification
                else {
                    Alert.alert('Notice', `You can only select up to ${maxImages} images`);
                }
            }

            return updatedImages;
        });
    }, [multiple, maxImages]);

    const confirmSelection = useCallback(() => {

        const selectedImages = galleryImages
            .filter(img => img.selected)
            .map(img => img);
        if (selectedImages.length === 0) {
            Alert.alert('Notice', 'Please select at least one image');
            return;
        }

        const uniqueImages = selectedImages.filter((image: any) => !isImageDuplicate(image, photosTaken));
        onSelect([...photosTaken, ...uniqueImages]);
        onClose();
    }, [galleryImages, onSelect, onClose]);

    // Delete selected images
    const deleteSelectedImages = useCallback(async () => {
        const selectedImages = galleryImages.filter(img => img.selected);

        if (selectedImages.length === 0) {
            Alert.alert('Notice', 'Please select images to delete');
            return;
        }

        try {
            for (const image of selectedImages) {
                await RNFS.unlink(image.uri);
            }

            // Reload photos after deletion
            await loadPhotos();

            Alert.alert('Success', 'Selected images have been deleted');
        } catch (error) {
            console.error('Error deleting images:', error);
            Alert.alert('Error', 'Failed to delete some images');
        }
    }, [galleryImages]);


    const renderGalleryItem = useCallback(({ item, index }: { item: GalleryImage; index: number }) => {
        return (
            <TouchableOpacity
                style={[
                    styles.imageItem,
                    item.selected && { borderColor: colors.primary, borderWidth: 2 }
                ]}
                onPress={() => toggleImageSelection(index)}
            >
                <Image source={{ uri: item.uri }} style={styles.thumbnail} />
                {item.selected && (
                    <View style={[styles.selectedIndicator, { backgroundColor: colors.primary }]}>
                        <CText style={styles.selectedText}>✓</CText>
                    </View>
                )}
            </TouchableOpacity>
        )
    }, [colors.primary, toggleImageSelection]);

    return (
        <Modal
            visible={visible}
            animationType="slide"
            onRequestClose={onClose}
        >
            <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={onClose}>
                        <CText style={[styles.headerButton, { color: colors.text }]}>Cancel</CText>
                    </TouchableOpacity>
                    <CText style={[styles.headerTitle, { color: colors.text }]}>
                        {multiple ? `${photosTaken.length}/${maxImages} images` : 'Select image'}
                    </CText>
                    <View style={styles.headerActions}>
                        <TouchableOpacity
                            onPress={deleteSelectedImages}
                            style={styles.deleteButton}
                        >
                            <CText style={[styles.headerButton, { color: colors.error }]}>Delete</CText>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={confirmSelection}>
                            <CText style={[styles.headerButton, { color: colors.primary }]}>Done</CText>
                        </TouchableOpacity>
                    </View>
                </View>

                <FlatList
                    data={galleryImages}
                    renderItem={renderGalleryItem}
                    keyExtractor={(item, index) => index.toString()}
                    numColumns={3}
                    contentContainerStyle={styles.imageGrid}
                />
            </SafeAreaView>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    headerButton: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },
    headerTitle: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.semiBold,
    },
    imageGrid: {
        padding: 4,
    },
    imageItem: {
        flex: 1,
        margin: 4,
        aspectRatio: 1,
        borderRadius: 8,
        overflow: 'hidden',
    },
    thumbnail: {
        width: '100%',
        height: '100%',
    },
    selectedIndicator: {
        position: 'absolute',
        top: 8,
        right: 8,
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    selectedText: {
        color: '#ffffff',
        fontWeight: FONT_WEIGHT.bold,
    },
    headerActions: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    deleteButton: {
        marginRight: 16,
    },
});

export default PhotoLibraryModal;