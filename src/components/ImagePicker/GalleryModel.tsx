// src/components/ImagePicker/GalleryModel.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Image,
    FlatList,
    Modal,
    SafeAreaView,
    Alert,
    Platform,
    PermissionsAndroid
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import CText from '@/components/CText';
import { FONT_WEIGHT } from '@/constants/UI/themes';
import RNFS from 'react-native-fs';
import { PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import { check } from 'react-native-permissions';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { isImageDuplicate } from '@/utils/imageUtils';

interface GalleryModelProps {
    visible: boolean;
    onClose: () => void;
    onSelect: (selectedImages: any[]) => void;
    multiple?: boolean;
    maxImages?: number;
    photosTaken?: any[];
}

interface GalleryImage {
    uri: string;
    selected: boolean;
    isFromApp: boolean;
}

const GalleryModel: React.FC<GalleryModelProps> = ({
    visible,
    onClose,
    onSelect,
    multiple = false,
    maxImages = 10,
    photosTaken = []
}) => {
    const { colors } = useTheme() as any;
    const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([]);


    // Load photos when modal becomes visible
    useEffect(() => {
        if (visible) {
            loadPhotos();
        }
    }, [visible]);


    // Add these new permission handling functions after the component interfaces
    const checkPhotoLibraryPermission = async () => {
        try {
            if (Platform.OS === 'ios') {
                const permission = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);
                switch (permission) {
                    case RESULTS.GRANTED:
                        return true;
                    case RESULTS.LIMITED:
                        return 'limited';
                    default:
                        return false;
                }
            } else {
                if (Number(Platform.Version) >= 33) {
                    const readMediaImages = await PermissionsAndroid.check(
                        PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
                    );
                    return readMediaImages;
                } else {
                    const readStorage = await PermissionsAndroid.check(
                        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
                    );
                    return readStorage;
                }
            }
        } catch (error) {
            console.error('Error checking permissions:', error);
            return false;
        }
    };

    const requestPhotoLibraryPermission = async () => {
        try {
            if (Platform.OS === 'ios') {
                const result = await request(PERMISSIONS.IOS.PHOTO_LIBRARY);
                return result === RESULTS.GRANTED || result === RESULTS.LIMITED;
            } else {
                if (Number(Platform.Version) >= 33) {
                    const result = await PermissionsAndroid.request(
                        PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
                        {
                            title: "Photo Access Permission",
                            message: "App needs access to your photos to select images",
                            buttonNeutral: "Ask Me Later",
                            buttonNegative: "Cancel",
                            buttonPositive: "OK"
                        }
                    );
                    return result === PermissionsAndroid.RESULTS.GRANTED;
                } else {
                    const result = await PermissionsAndroid.request(
                        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
                        {
                            title: "Photo Access Permission",
                            message: "App needs access to your photos to select images",
                            buttonNeutral: "Ask Me Later",
                            buttonNegative: "Cancel",
                            buttonPositive: "OK"
                        }
                    );
                    return result === PermissionsAndroid.RESULTS.GRANTED;
                }
            }
        } catch (error) {
            console.error('Error requesting permissions:', error);
            return false;
        }
    };

    // Update the loadPhotos function
    const loadPhotos = async () => {
        try {
            // First check permissions
            const permissionStatus = await checkPhotoLibraryPermission();
            console.log('permissionStatus', permissionStatus)
            if (permissionStatus === false) {
                // Request permission if not granted
                const granted = await requestPhotoLibraryPermission();
                if (!granted) {
                    Alert.alert(
                        'Permission Required',
                        'The app needs photo library access to select images',
                        [{ text: 'OK' }]
                    );
                    onClose();
                    return;
                }
            }

            const result = await CameraRoll.getPhotos({
                first: 99,
                assetType: 'Photos',
                include: [
                    'filename',
                    'fileSize',
                    'fileExtension',
                    'location',
                    'imageSize',
                    'orientation',
                ],
            });
            console.log('result', result)
            setGalleryImages(result.edges.map((edge: any) => ({
                uri: edge.node.image.uri,
                selected: photosTaken.some((photo: any) => photo.uri === edge.node.image.uri),
                isFromApp: true,
            })))

        } catch (error) {
            console.error('Error loading photos:', error);
            Alert.alert('Error', 'Unable to load photos');
            onClose();
        }
    };


    const toggleImageSelection = useCallback((index: number) => {
        setGalleryImages(prev => {
            const updatedImages = [...prev];

            if (!multiple) {
                // If not selecting multiple images, deselect all other images
                updatedImages.forEach((img, i) => {
                    updatedImages[i].selected = i === index;
                });
            } else {
                // If selecting multiple images
                const selectedCount = updatedImages.filter(img => img.selected).length;

                // If image is already selected, deselect it
                if (updatedImages[index].selected) {
                    updatedImages[index].selected = false;
                }
                // If image is not selected and haven't reached the limit, select it
                else if (selectedCount < maxImages) {
                    updatedImages[index].selected = true;
                }
                // If reached the limit, show notification
                else {
                    Alert.alert('Notice', `You can only select up to ${maxImages} images`);
                }
            }

            return updatedImages;
        });
    }, [multiple, maxImages]);

    const confirmSelection = useCallback(() => {

        const selectedImages = galleryImages
            .filter(img => img.selected)
            .map(img => img);
        if (selectedImages.length === 0) {
            Alert.alert('Notice', 'Please select at least one image');
            return;
        }
        const uniqueImages = selectedImages.filter((image: any) => !isImageDuplicate(image, photosTaken));
        onSelect([...photosTaken, ...uniqueImages]);
        onClose();
    }, [galleryImages, onSelect, onClose]);

    // Delete selected images
    const deleteSelectedImages = useCallback(async () => {
        const selectedImages = galleryImages.filter(img => img.selected);

        if (selectedImages.length === 0) {
            Alert.alert('Notice', 'Please select images to delete');
            return;
        }

        try {
            for (const image of selectedImages) {
                await RNFS.unlink(image.uri);
            }

            // Reload photos after deletion
            await loadPhotos();

            Alert.alert('Success', 'Selected images have been deleted');
        } catch (error) {
            console.error('Error deleting images:', error);
            Alert.alert('Error', 'Failed to delete some images');
        }
    }, [galleryImages]);


    const renderGalleryItem = useCallback(({ item, index }: { item: GalleryImage; index: number }) => {
        return (
            <TouchableOpacity
                style={[
                    styles.imageItem,
                    item.selected && { borderColor: colors.primary, borderWidth: 2 }
                ]}
                onPress={() => toggleImageSelection(index)}
            >
                <Image source={{ uri: item.uri }} style={styles.thumbnail} />
                {item.selected && (
                    <View style={[styles.selectedIndicator, { backgroundColor: colors.primary }]}>
                        <CText style={styles.selectedText}>✓</CText>
                    </View>
                )}
            </TouchableOpacity>
        )
    }, [colors.primary, toggleImageSelection]);

    return (
        <Modal
            visible={visible}
            animationType="slide"
            onRequestClose={onClose}
        >
            <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={onClose}>
                        <CText style={[styles.headerButton, { color: colors.text }]}>Cancel</CText>
                    </TouchableOpacity>
                    <CText style={[styles.headerTitle, { color: colors.text }]}>
                        {multiple ? `Select up to ${maxImages} images` : 'Select image'}
                    </CText>
                    <View style={styles.headerActions}>
                        <TouchableOpacity
                            onPress={deleteSelectedImages}
                            style={styles.deleteButton}
                        >
                            <CText style={[styles.headerButton, { color: colors.error }]}>Delete</CText>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={confirmSelection}>
                            <CText style={[styles.headerButton, { color: colors.primary }]}>Done</CText>
                        </TouchableOpacity>
                    </View>
                </View>

                <FlatList
                    data={galleryImages}
                    renderItem={renderGalleryItem}
                    keyExtractor={(item, index) => index.toString()}
                    numColumns={3}
                    contentContainerStyle={styles.imageGrid}
                />
            </SafeAreaView>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    headerButton: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },
    headerTitle: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.semiBold,
    },
    imageGrid: {
        padding: 4,
    },
    imageItem: {
        flex: 1,
        margin: 4,
        aspectRatio: 1,
        borderRadius: 8,
        overflow: 'hidden',
    },
    thumbnail: {
        width: '100%',
        height: '100%',
    },
    selectedIndicator: {
        position: 'absolute',
        top: 8,
        right: 8,
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    selectedText: {
        color: '#ffffff',
        fontWeight: FONT_WEIGHT.bold,
    },
    headerActions: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    deleteButton: {
        marginRight: 16,
    },
});

export default GalleryModel;