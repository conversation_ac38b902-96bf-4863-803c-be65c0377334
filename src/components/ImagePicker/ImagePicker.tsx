import React, { useState, useCallback, useEffect } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Text,
    Modal,
    ImageBackground,
    Platform,
} from 'react-native';
import { useNavigation, useTheme } from '@react-navigation/native';
import { format } from 'date-fns';
import CText from '@/components/CText';
import { FONT_WEIGHT } from '@/constants/UI/themes';
import CameraBase from '../CameraBase';
import GalleryModel from './GalleryModel';
import { PermissionType, usePermission } from '@/hooks/usePermission';
import { getResponsiveFontSize } from '@/constants/UI/responsive';
import { SPACING, COLORS, ZINDEX } from '@/constants/UI/themes';
import CIcon from '../CIcon';
import { Icamera, Iimage } from '@/constants/UI/icons';
import { SCREEN_NAMES } from '@/constants/navigation';

interface ImagePickerProps {
    onImagesSelected: (images: any[]) => void;
    multiple?: boolean;
    maxImages?: number;
    mode?: 'photo' | 'scan';
    initialImages?: any[];
}

const ImagePicker: React.FC<ImagePickerProps> = ({
    onImagesSelected,
    multiple = true,
    maxImages = 10,
    mode = 'photo',
    initialImages = [],
}) => {
    const [cameraVisible, setCameraVisible] = useState(false);
    const [galleryVisible, setGalleryVisible] = useState(false);
    const [selectedPhoto, setSelectedPhoto] = useState<any>(null);
    const [photosTaken, setPhotosTaken] = useState<any[]>(initialImages);
    const { colors } = useTheme();
    const navigation = useNavigation<any>();
    // Initialize permission hooks
    const cameraPermission = usePermission('camera');
    const storagePermission = usePermission('storage');

    useEffect(() => {
        if (JSON.stringify(photosTaken) !== JSON.stringify(initialImages)) {
            setPhotosTaken(initialImages);
        }
    }, [initialImages]);

    const handleCameraPress = useCallback(async () => {
        const granted = await cameraPermission.request();
        if (granted) {
            navigation.navigate(SCREEN_NAMES.CAMERA, { isScan: false, photosTaken: photosTaken } as never);
        }
    }, [cameraPermission]);

    const handleGalleryPress = useCallback(async () => {
        const granted = await storagePermission.request();
        if (granted) {
            setGalleryVisible(true);
        }
    }, [storagePermission]);

    const handlePhotoTaken = useCallback((uri: string) => {
        const newImage = { uri, filename: `Photo_${Date.now()}.jpg` };
        const updatedImages = [...photosTaken, newImage];
        setPhotosTaken(updatedImages);
        onImagesSelected(updatedImages);
        setCameraVisible(false);
    }, [photosTaken, onImagesSelected]);

    const handleGallerySelect = useCallback((selectedImages: any[]) => {
        setPhotosTaken(selectedImages);
        onImagesSelected(selectedImages);
        setGalleryVisible(false);
    }, [onImagesSelected]);

    const handleClosePhotoView = useCallback(() => {
        setSelectedPhoto(null);
    }, []);

    return (
        <>
            {/* Action Buttons */}
            <View style={styles.actionContainer}>
                <TouchableOpacity style={styles.button} onPress={handleCameraPress}>
                    <CText style={styles.buttonText}>Take Photo</CText>
                    <CIcon source={Icamera} size={24} tintColor={COLORS.light.white} />
                </TouchableOpacity>
                <View style={{ width: 10 }} />
                <TouchableOpacity style={styles.button} onPress={handleGalleryPress}>
                    <CText style={styles.buttonText}>Add Photo</CText>
                    <CIcon source={Iimage} size={26} tintColor={COLORS.light.white} />

                </TouchableOpacity>
            </View>

            {/* Gallery Modal */}
            <GalleryModel
                visible={galleryVisible}
                onClose={() => setGalleryVisible(false)}
                onSelect={handleGallerySelect}
                multiple={multiple}
                maxImages={maxImages}
                photosTaken={photosTaken}
            />

            {/* Photo Preview Modal */}
            <Modal
                visible={!!selectedPhoto}
                transparent={true}
                animationType="fade"
                onRequestClose={handleClosePhotoView}
            >
                <View style={styles.modalContainer}>
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={handleClosePhotoView}
                    >
                        <CText style={styles.closeButtonText}>✕</CText>
                    </TouchableOpacity>

                    {selectedPhoto && (
                        <ImageBackground
                            source={{ uri: selectedPhoto.uri }}
                            style={styles.fullImage}
                            resizeMode="contain"
                        >
                            <TouchableOpacity
                                style={StyleSheet.absoluteFill}
                                activeOpacity={1}
                                onPress={handleClosePhotoView}
                            />
                        </ImageBackground>
                    )}

                    {selectedPhoto && (
                        <View style={styles.photoDetailsContainer}>
                            <CText style={styles.photoDetailsTitle}>
                                {selectedPhoto?.filename || 'Unknown'}
                            </CText>
                            <View style={styles.photoDetailsRow}>
                                <View style={styles.detailItem}>
                                    <CText style={styles.detailText}>
                                        {format(new Date(), 'dd/MM/yyyy HH:mm')}
                                    </CText>
                                </View>
                            </View>
                        </View>
                    )}
                </View>
            </Modal>
        </>
    );
};

const styles = StyleSheet.create({
    actionContainer: {
        flex: 1,
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        width: '100%',
    },
    button: {
        backgroundColor: COLORS.light.primary,
        paddingVertical: SPACING.sm,
        paddingHorizontal: SPACING.md,
        borderRadius: 5,
        minWidth: 120,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        width: '49%',
    },
    buttonText: {
        color: COLORS.light.white,
        fontSize: getResponsiveFontSize(16),
        fontWeight: FONT_WEIGHT.medium,
        marginRight: SPACING.sm,
    },
    cameraContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: ZINDEX.modal,
    },
    modalContainer: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.9)',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 40 : 20,
        right: 20,
        zIndex: ZINDEX.modal,
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    closeButtonText: {
        color: COLORS.light.white,
        fontSize: getResponsiveFontSize(18),
        fontWeight: FONT_WEIGHT.bold,
    },
    fullImage: {
        width: '100%',
        height: '80%',
    },
    photoDetailsContainer: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.7)',
        padding: SPACING.md,
    },
    photoDetailsTitle: {
        color: COLORS.light.white,
        fontSize: getResponsiveFontSize(18),
        fontWeight: FONT_WEIGHT.semiBold,
        marginBottom: SPACING.sm,
    },
    photoDetailsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 4,
    },
    detailText: {
        color: COLORS.light.white,
        fontSize: getResponsiveFontSize(14),
        marginLeft: SPACING.sm,
    },
});

export default ImagePicker; 