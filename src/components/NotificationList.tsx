import React from 'react';
import { FlatList, StyleSheet, Dimensions, View } from 'react-native';
import NotificationItem from './NotificationItem';
import { useTheme } from '@react-navigation/native';
import { AppTheme } from '@/types';
const { colors } = useTheme() as unknown as AppTheme;

const { width, height } = Dimensions.get('window');
const HEADER_HEIGHT = 33;

interface NotificationItemType {
    id: string;
    title: string;
    date: string;
    desc: string;
    unread: boolean;
}

interface NotificationListProps {
    notifications: NotificationItemType[];
}

const NotificationList: React.FC<NotificationListProps> = ({
    notifications,
}) => {
    return (
        <View style={styles.sheet}>
            <FlatList
                data={notifications}
                keyExtractor={item => item.id}
                renderItem={({ item, index }) => (
                    <NotificationItem
                        title={item.title}
                        date={item.date}
                        desc={item.desc}
                        unread={item.unread}
                        isLast={index === notifications.length - 1}
                    />
                )}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 24 }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    sheet: {
        flex: 1,
        backgroundColor: colors.white,
        borderTopLeftRadius: 32,
        borderTopRightRadius: 32,
        paddingHorizontal: 30,
        paddingTop: 32,
        elevation: 3,
        width: width,
        maxHeight: height - HEADER_HEIGHT,
    },
});

export default NotificationList;