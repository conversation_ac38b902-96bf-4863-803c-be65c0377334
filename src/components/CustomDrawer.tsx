import React, { useCallback, useRef, useState } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Alert
} from 'react-native';
import {
    DrawerContentScrollView,
    DrawerContentComponentProps,
} from '@react-navigation/drawer';
import { useTheme } from '@react-navigation/native';
import CText from './CText';
import CIcon from './CIcon';
import { useTranslation } from 'react-i18next';
import { useResponsiveUtils } from '@/constants/UI/responsive';
import { FONT_SIZE, SPACING, PADDING, FONT_WEIGHT, COLORS } from '@/constants/UI/themes';
import { Iback, Ibatch, Icustomer, Igps, Ihome, Ilogout, Ipackage, Ipeople, Ireport, Isetting } from '@/constants/UI/icons';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';
import ProfileAvatar from './ProfileAvatar';
import LinearGradient from 'react-native-linear-gradient';
import { AppTheme } from '@/types';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUser } from '@/redux/slices/authSlice';
import { AppDispatch, RootState } from '@/redux/store';
import { useToast } from '@/hocs/toast';
import { debounce } from 'lodash';
import { SCREEN_NAMES } from '@/constants/navigation';

const { colors } = useTheme() as unknown as AppTheme;

interface MenuItem {
    id: string;
    label: string;
    icon: string;
    isLogout?: boolean;
    isLanguage?: boolean;
}

// Tạo debounced function bên ngoài component
const debouncedNavigate = debounce((navigation, screenId) => {
    navigation.navigate(screenId);
}, 300, { leading: true, trailing: false });

const CustomDrawer = (props: DrawerContentComponentProps) => {
    const { t } = useTranslation();
    const { scale, pixelSize, width, isLandscape } = useResponsiveUtils();
    const dispatch = useDispatch<AppDispatch>();
    const { data } = useSelector((state: RootState) => state.user);
    const { showToast } = useToast();
    const menuItems: MenuItem[] = [
        { label: t('menu.home'), icon: Ihome, id: SCREEN_NAMES.HOME },
        { label: t('menu.batch'), icon: Ibatch, id: SCREEN_NAMES.BATCH },
        { label: t('menu.customer'), icon: Ipeople, id: SCREEN_NAMES.CUSTOMER },
        { label: t('menu.inventory'), icon: Ipackage, id: SCREEN_NAMES.INVENTORY },
        { label: t('menu.report'), icon: Ireport, id: SCREEN_NAMES.REPORT },
        { label: t('menu.settings'), icon: Isetting, id: SCREEN_NAMES.SETTINGS },
        { label: t('auth.logout'), icon: Ilogout, isLogout: true, id: '' },
    ];

    const handleLogout = () => {
        Alert.alert(
            t('auth.logoutConfirmation'),
            '',
            [
                {
                    text: t('common.cancel'),
                    style: 'cancel'
                },
                {
                    text: t('common.logout'),
                    onPress: () => {
                        dispatch(logoutUser()).unwrap()
                            .then((result: any) => {
                                if (result.success) {
                                    showToast(result.data.message, 'success');
                                    props.navigation.closeDrawer();
                                }
                            })
                            .catch((error) => {
                                if (error && typeof error === 'object') {
                                    console.error('Logout failed:', error);
                                    Alert.alert(
                                        t('errors.logoutError'),
                                        t('errors.somethingWentWrong')
                                    );
                                }
                            });
                    }
                }
            ]
        );
    };

    const handlePress = useCallback((item: MenuItem) => {
        if (item.isLogout) {
            handleLogout();
        } else if (item.id) {
            debouncedNavigate(props.navigation, item.id);
        }
    }, [props.navigation]);

    const renderDrawerItem = useCallback((item: MenuItem, index: number) => (
        <View key={index}>
            <TouchableOpacity
                style={[
                    styles.menuItem,
                    item.isLogout && styles.logoutItem,
                    { marginBottom: SPACING.md }
                ]}
                onPress={() => handlePress(item)}
            >
                <CIcon
                    source={item.icon}
                    size={pixelSize(FONT_SIZE.size40)}
                    style={styles.menuIcon}
                />
                <CText
                    style={[
                        styles.menuText,
                        { color: item.isLogout ? colors.error : colors.gray },
                        { fontSize: pixelSize(FONT_SIZE.size20), fontWeight: FONT_WEIGHT.medium },
                    ]}
                >
                    {item.label}
                </CText>
            </TouchableOpacity>
            
            {index < menuItems.length - 1 && (
                <LinearGradient
                    colors={['#E0F5F5', '#A7E4EC', '#E0F5F5']}
                    locations={[0, 0.5, 1]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.menuItemBorder}
                />
            )}
        </View>
    ), [handlePress, menuItems.length, pixelSize, colors]);

    return (
        <View style={[styles.container]}>
            {/* Header */}
            <LinearGradient
                colors={colors.backgroundGradient}
                locations={[0.1, 0.9]}
                style={styles.header}>
                <TouchableOpacity
                    onPress={() => props.navigation.closeDrawer()}
                    style={styles.backButton}
                >
                    <CIcon
                        source={Iback}
                        size={pixelSize(FONT_SIZE.size40)}
                        style={styles.backIcon}
                    />
                </TouchableOpacity>
                <View style={styles.headerContent}>
                    <View style={styles.userInfo}>
                        <ProfileAvatar
                            style={styles.avatar}
                            name={data?.name}
                        />
                        <View style={styles.userTextContainer}>
                            <CText
                                size="size26"
                                style={[
                                    styles.userName,
                                    {
                                        color: colors.black,
                                        fontWeight: FONT_WEIGHT.medium,
                                        fontSize: FONT_SIZE.xxl,
                                    }
                                ]}
                            >
                                {data?.name}
                            </CText>
                            <CText
                                size="size20"
                                style={[
                                    styles.userEmail,
                                    {
                                        color: colors.text,
                                        fontWeight: FONT_WEIGHT.medium,
                                        opacity: 0.8
                                    }
                                ]}
                            >
                                {data?.job_title}
                            </CText>
                        </View>
                    </View>
                </View>
            </LinearGradient>

            {/* Drawer Items */}
            <LinearGradient
                colors={['#FFFFFF', '#CCF6F6']}
                locations={[0, .7]}
                style={styles.drawerGradient}
            >
                <DrawerContentScrollView
                    {...props}
                    contentContainerStyle={[
                        styles.drawerContent,
                        {
                            backgroundColor: 'transparent',
                        },
                    ]}
                    style={styles.drawer}
                >
                    {menuItems.map((item, index) => renderDrawerItem(item, index))}
                </DrawerContentScrollView>
            </LinearGradient>

        </View>

    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        position: 'relative',
    },
    header: {
        flexDirection: 'row',
        height: 221,
        position: 'relative',
    },
    backButton: {
        position: 'absolute',
        left: 30,
        top: 71,
        zIndex: 1,
    },
    backIcon: {
        padding: SPACING.xs,
    },
    headerContent: {
        flex: 1,
        ...BASE_STYLE.centerContent,
        paddingTop: 73,
        height: 221,
    },
    userInfo: {
        ...BASE_STYLE.centerContent,
        // width: 123,
        height: 128,
        
    },
    avatar: {
        marginBottom: SPACING.xs,
        borderWidth: 1,
        borderColor: colors.avatarBorder,
        backgroundColor: colors.avatarBackground,
    },
    userTextContainer: {
        ...BASE_STYLE.centerContent,
    },
    userName: {
        textAlign: 'center',
    },
    userEmail: {
        textAlign: 'center',
    },
    drawer: {
        paddingInline: SPACING.lg
    },
    drawerContent: {
        flexGrow: 1,
        marginTop: 20
    },
    menuItem: {
        ...BASE_STYLE.rowCenter,
    },
    menuIcon: {
        marginRight: SPACING.sm,
        width: 32,
    },
    menuText: {},
    logoutItem: {},
    logoutLine: {
        position: 'absolute',
        bottom: 0,
        left: SPACING.md,
        right: SPACING.md,
        height: 2,
        backgroundColor: colors.error,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        ...BASE_STYLE.centerContent,
    },
    modalContent: {
        width: '80%',
        padding: PADDING.lg,
        borderRadius: 10,
        elevation: 5,
    },
    modalTitle: {
        textAlign: 'center',
        marginBottom: SPACING.lg,
        fontWeight: 'bold',
    },
    languageOption: {
        padding: PADDING.md,
        marginVertical: SPACING.xs,
        borderRadius: 5,
        backgroundColor: '#F5F5F5',
    },

    closeButton: {
        marginTop: SPACING.lg,
        padding: PADDING.sm,
        backgroundColor: '#E6E6E6',
        borderRadius: 5,
    },
    closeButtonText: {
        textAlign: 'center',
        color: '#333',
    },
    drawerGradient: {
        flex: 1,
    },
    menuItemBorder: {
        height: 1,
        marginLeft: SPACING.md,
        marginRight: SPACING.md,
        marginBottom: SPACING.md,
        borderRadius: 1,
    },
});

export default CustomDrawer;