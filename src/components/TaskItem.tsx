import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@react-navigation/native';
import CCard from './CCard';
import CIcon from './CIcon';
import { SPACING, COLORS, FONT_WEIGHT } from '../constants/UI/themes';
import { Iclock, Igps, Ipackage, Iperson, Isetting } from '@/constants/UI/icons';
import { Task } from '@/interfaces/task';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { DrawerParamList } from '@/types/navigation';
import { adjustHeight, getResponsiveFontSize } from '@/constants/UI/responsive';
import { SCREEN_NAMES } from '@/constants/navigation';

interface TaskItemProps {
    item: Task;
}

type TaskNavigationProp = DrawerNavigationProp<DrawerParamList, 'TaskDetail'>;

const TaskItem = ({ item }: TaskItemProps) => {
    const theme = useTheme();
    const navigation = useNavigation<TaskNavigationProp>();
    const estimatedTime = '30 mins';
    const distance = '2.5 miles';
    const { width: screenWidth } = Dimensions.get('window');

    const handleTaskPress = () => {
        navigation.navigate(SCREEN_NAMES.DETAIL_TASK as any, { task: item });
    };

    return (
        <TouchableOpacity
            style={styles.itemWrapper}
            onPress={handleTaskPress}
            activeOpacity={0.7}
        >
            <CCard style={[styles.card, { minWidth: screenWidth, borderRadius: 0 }]}>

                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                    <View style={[styles.taskCodeContainer, { width: "80%" }]}>
                        <View style={[styles.taskCodeContainer]}>
                            <Text style={styles.taskCode}>{item.task_code}</Text>
                        </View>

                    </View>

                    <View style={styles.statusContainer}>
                        <Text style={[styles.status, { color: theme.colors.primary }]}>
                            {item.status === 'scheduled' ? 'Pending' : item.status === 'completed' ? 'Completed' : 'In Progress'}
                        </Text>
                    </View>
                </View>

                <View style={styles.customerInfo}>
                    <CIcon source={Iperson} size={20} tintColor={theme.colors.text} />
                    <Text style={styles.customerName}>{item.customer.name}</Text>
                </View>

                <View style={styles.customerInfo}>
                    <CIcon source={Igps} size={20} tintColor={theme.colors.text} />
                    <Text style={styles.customerName}>{item.customer.address}</Text>
                </View>

                <View style={styles.productsContainer}>
                    <CIcon source={Ipackage} size={20} tintColor={theme.colors.text} />
                    <Text style={styles.productsText}>
                        {item.products
                            ? (item.products.length > 2
                                ? `${item.products[0]}, ${item.products[1]}...`
                                : item.products.join(', '))
                            : item.repair_product}
                    </Text>
                </View>

                <View style={styles.footer}>
                    <View style={{ flexDirection: 'row' }}>
                        <CIcon source={Iclock} size={20} tintColor={theme.colors.text} style={{ marginRight: SPACING.sm }} />
                        <Text style={styles.timeText}>{estimatedTime}</Text>
                    </View>
                    <View>
                        <Text style={styles.distanceText}>{distance}</Text>
                    </View>
                </View>
            </CCard>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    itemWrapper: {
        width: '100%',
        alignItems: 'center',
    },
    card: {
        // minWidth: 380,
        // minHeight: adjustHeight(204),
        minHeight: adjustHeight(100),
        backgroundColor: COLORS.light.white,
        padding: SPACING.md,
    },
    statusContainer: {
        // position: 'absolute',
        // top: SPACING.md,
        // right: SPACING.md,
    },
    status: {
        fontSize: 14,
        fontWeight: '600',
        flexGrow: 1,
    },
    taskCode: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: SPACING.sm,
    },
    customerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.xs,
        width: '100%',
    },
    customerName: {
        marginLeft: SPACING.xs,
        fontSize: getResponsiveFontSize(18),
    },
    productsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.sm,
    },
    productsText: {
        marginLeft: SPACING.xs,
        fontSize: getResponsiveFontSize(18),
        flex: 1,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        // marginTop: 'auto',
        width: '100%',
    },
    timeText: {
        fontSize: 14,
        color: COLORS.light.text,
    },
    distanceText: {
        fontSize: 14,
        color: COLORS.light.textSecondary,
        fontWeight: FONT_WEIGHT.medium,
    },
    taskCodeContainer: {

    },
});

export default TaskItem; 