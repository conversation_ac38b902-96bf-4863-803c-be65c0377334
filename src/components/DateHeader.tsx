import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Pressable, ViewStyle, TextStyle, Animated, Easing } from 'react-native';
import { format } from 'date-fns';
import { SPACING, COLORS } from '@/constants/UI/themes';
import CIcon from './CIcon';
import { Iarrow, Icalender } from '@/constants/UI/icons';
import { useTheme } from '@react-navigation/native';

interface DateHeaderProps {
    date: Date;
    onPress?: () => void;
    onIconPress?: () => void;
    showIcon?: boolean;
    iconSource?: any;
    containerStyle?: ViewStyle;
    textStyle?: TextStyle;
    dateFormat?: string;
    iconSize?: number;
    iconColor?: string;
    isCalendarVisible?: boolean;
}

export const DateHeader: React.FC<DateHeaderProps> = ({
    date,
    onPress,
    onIconPress,
    showIcon = true,
    iconSource = Iarrow,
    containerStyle,
    textStyle,
    dateFormat = 'MMMM d, yyyy',
    iconSize = 24,
    iconColor,
    isCalendarVisible = false,
}) => {
    const rotateAnim = React.useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        Animated.timing(rotateAnim, {
            toValue: isCalendarVisible ? 1 : 0,
            duration: 100,
            easing: Easing.ease,
            useNativeDriver: true,
        }).start();
    }, [isCalendarVisible, rotateAnim]);

    const rotate = rotateAnim.interpolate({
        inputRange: [0, 1],
        outputRange: ['90deg', '180deg'],
    });

    const handleIconPress = (e: any) => {
        e.stopPropagation();
        if (onIconPress) {
            onIconPress();
        } else if (onPress) {
            onPress();
        }
    };

    return (
        <Pressable
            onPress={onPress}
            style={[styles.container, containerStyle]}
        >
            <Text style={[styles.dateText, textStyle]}>
                {format(date, dateFormat)}
            </Text>

            {showIcon && (
                <TouchableOpacity
                    onPress={handleIconPress}
                    style={styles.iconButton}
                >
                    <Animated.View style={{ transform: [{ rotate }] }}>
                        <CIcon
                            source={iconSource}
                            size={iconSize}
                            tintColor={iconColor || COLORS.light.text}
                        />
                    </Animated.View>
                </TouchableOpacity>
            )}
        </Pressable>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: SPACING.xs,
    },
    dateText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.light.text,
    },
    iconButton: {
        padding: SPACING.xs,
    },
}); 