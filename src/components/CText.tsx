import React from 'react';
import { Text, TextProps } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { FONT_FAMILY, FONT_SIZE, FONT_WEIGHT } from '@/constants/UI/themes';
import { AppTheme } from '@/types/theme';

interface CTextProps extends TextProps {
    children: React.ReactNode;
    weight?: keyof typeof FONT_WEIGHT;
    size?: keyof typeof FONT_SIZE;
}

const CText: React.FC<CTextProps> = ({ children, style, weight = 'semiBold', size = 'lg', ...props }) => {
    const { colors, fonts } = useTheme() as unknown as AppTheme;

    return (
        <Text
            style={[
                {
                    color: colors.text,
                    fontFamily: fonts.bodyLarge.fontFamily,
                    fontSize: fonts.bodyLarge.fontSize,
                },
                style,
            ]}
            {...props}
        >
            {children}
        </Text>
    );
};

export default CText;
