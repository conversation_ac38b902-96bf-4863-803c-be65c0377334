import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { format, startOfWeek, addDays } from 'date-fns';
import { SPACING, COLORS, FONTS } from '@/constants/UI/themes';
import { useTranslation } from 'react-i18next';
import { adjustHeight, adjustWidth, defaultHeight, defaultWidth, scale } from '@/constants/UI/responsive';

interface WeekDaysProps {
    selectedDate: Date;
    onDayPress: (date: Date) => void;
}

export const WeekDays: React.FC<WeekDaysProps> = ({ selectedDate, onDayPress }) => {
    const { t } = useTranslation();

    // Caculate the week days based on the selected date
    const weekDays = useMemo(() => {
        const start = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Start from Monday
        return Array.from({ length: 7 }, (_, i) => addDays(start, i));
    }, [selectedDate]);

    // Function to return the key corresponding to the day of the week
    const getDayKey = (day: number): string => {
        const keys = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        return keys[day - 1] || 'mon'; // Return 'mon' if not found
    };

    // Function to return the abbreviated name of the day according to the current language
    const getWeekdayName = (date: Date): string => {
        const day = date.getDay(); // 0 = Sunday, 1 = Monday, ...
        // Adjust to 1 = Tuesday, ..., 7 = Sunday
        const adjustedDay = day === 0 ? 7 : day;
        const key = getDayKey(adjustedDay);
        return t(`weekDays.${key}`);
    };


    return (
        <View style={styles.container}>
            {weekDays.map((date, index) => {
                const isSelected = format(date, 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd');
                return (
                    <TouchableOpacity
                        key={index}
                        style={[
                            styles.dayContainer,
                            isSelected && styles.selectedDayContainer
                        ]}
                        onPress={() => onDayPress(date)}
                    >
                        <Text style={[
                            styles.dayName,
                            isSelected && styles.selectedText
                        ]}>
                            {getWeekdayName(date)}
                        </Text>

                        <View style={[
                            styles.separator,
                            isSelected && styles.selectedSeparator
                        ]} />

                        <Text style={[
                            styles.dayNumber,
                            isSelected && styles.selectedText
                        ]}>
                            {format(date, 'd')}
                        </Text>
                    </TouchableOpacity>
                );
            })}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        // backgroundColor: COLORS.light.white,
        paddingHorizontal: adjustWidth(14),
        paddingVertical: SPACING.sm,
    },
    dayContainer: {
        width: adjustWidth((defaultWidth / 7) - 10),
        height: adjustHeight(74),
        borderRadius: 14.25,
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: COLORS.light.white,
        // Shadow for iOS
        // shadowColor: '#000000',
        // shadowOffset: { width: 0, height: 2.85 },
        // shadowOpacity: 0.25,
        // shadowRadius: 2.85,
        // Shadow for Android
        // elevation: 4,
        paddingVertical: 8,
        borderWidth: 1,
        borderColor: COLORS.light.border,
    },
    selectedDayContainer: {
        backgroundColor: COLORS.light.primary,
    },
    dayName: {
        fontFamily: 'Lato',
        fontWeight: '400',
        fontSize: 18,
        lineHeight: 18, // 100% of font size
        textAlign: 'center',
        color: COLORS.light.text,
    },
    separator: {
        width: 29,
        borderWidth: 0.71,
        borderColor: COLORS.light.border,
        marginVertical: 7.12, // Using the gap value as marginVertical
    },
    selectedSeparator: {
        borderColor: COLORS.light.white,
    },
    dayNumber: {
        fontFamily: 'Lato',
        fontWeight: '400',
        fontSize: 18,
        lineHeight: 18, // 100% of font size
        textAlign: 'center',
        color: COLORS.light.text,
    },
    selectedText: {
        color: COLORS.light.white,
    },
}); 