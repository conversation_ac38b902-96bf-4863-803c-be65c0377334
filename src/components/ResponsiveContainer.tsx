import React from 'react';
import { 
    View, 
    StyleSheet, 
    ViewStyle, 
    StyleProp, 
    useWindowDimensions, 
    DimensionValue, 
    Platform,
    ScrollView,
} from 'react-native';
import { INSETS, LAYOUT } from '../constants/UI/themes';
import { getBreakpoint, isLandscape } from '../constants/UI/responsive';
import { DeviceType } from '../types';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getMaxWidth } from '../utils/layout';

interface ResponsiveContainerProps {
    children: React.ReactNode;
    style?: StyleProp<ViewStyle>;
    fluid?: boolean; // If true, container will be full width
    ignoreBottomInset?: boolean; // If true, will not add bottom padding for navigation bar
    scrollEnabled?: boolean; // If true, container will be scrollable
}

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
    children,
    style,
    fluid = false,
    ignoreBottomInset = false,
    scrollEnabled = true,
}) => {
    const { width, height } = useWindowDimensions();
    const breakpoint: DeviceType = getBreakpoint(width);
    const landscape = isLandscape(width, height);
    const insets = useSafeAreaInsets();

    const getContainerStyle = (): ViewStyle => {
        const baseStyle: ViewStyle = {
            paddingBottom: ignoreBottomInset ? 0 : (
                Platform.OS === 'android'
                    ? insets.bottom + INSETS.navigation.android
                    : insets.bottom
            ),
        };

        if (fluid) {
            return {
                ...baseStyle,
                width: '100%' as DimensionValue,
                paddingHorizontal: landscape
                    ? LAYOUT.containerPadding.landscape
                    : LAYOUT.containerPadding.portrait,
            };
        }

        const maxWidth = getMaxWidth(breakpoint, width);

        return {
            ...baseStyle,
            width: '100%' as DimensionValue,
            maxWidth,
            marginHorizontal: 'auto' as DimensionValue,
        };
    };

    const Container = scrollEnabled ? ScrollView : View;

    return (
        <Container 
            style={[styles.container, getContainerStyle(), style]}
            contentContainerStyle={scrollEnabled ? styles.scrollContent : undefined}
            showsVerticalScrollIndicator={true}
            scrollEventThrottle={16}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
        >
            {children}
        </Container>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContent: {
        flexGrow: 1,
    },
});

export default ResponsiveContainer; 