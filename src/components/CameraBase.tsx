import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Alert,
    Platform,
    Image,
    Dimensions,
    Animated as RNAnimated,
    Linking
} from 'react-native';
import {
    Camera,
    useCameraPermission,
    useCodeScanner,
    Point,
    useCameraFormat,
    CodeType
} from 'react-native-vision-camera';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle, useSharedValue, withTiming, runOnJS } from 'react-native-reanimated';
import { useNavigation, useRoute, useTheme, useFocusEffect } from '@react-navigation/native';
import CText from '@/components/CText';
import { FONT_WEIGHT } from '@/constants/UI/themes';
import PhotoLibraryModal from './ImagePicker/PhotoLibraryModal';
import { isIOS } from '@/constants/UI/responsive';
import * as RNFS from 'react-native-fs';
import { SCREEN_NAMES } from '@/constants/navigation';
import { mockTasks } from '@/constants/mock/tasks';
import { isImageDuplicate } from '@/utils/imageUtils';
const { width } = Dimensions.get('window');

// Types
export interface CameraBaseProps {
    onCodeScanned?: (code: string) => void;
    onPhotoTaken?: (uri: string) => void;
    onClose?: () => void;
    initialCameraPosition?: 'back' | 'front';
    initialFlashMode?: 'auto' | 'on' | 'off';
    codeTypes?: CodeType[];
    disablePhotoGallery?: boolean;
    showCloseButton?: boolean;
    scanFrameSize?: { width: number; height: number };
    photosTaked?: any[];
    navigation?: any;
}

export interface ScanResult {
    value: string;
    timestamp: number;
}

interface IosPhotoInfo {
    height: number;
    width: number;
    isMirrored: boolean;
    isRawPhoto: boolean;
    orientation: 'portrait' | 'landscape';
    path: string;
}

const DEFAULT_CODE_TYPES: CodeType[] = ['qr', 'ean-13', 'ean-8', 'code-128', 'code-39', 'code-93', 'codabar', 'itf', 'upc-e'];
const DEFAULT_SCAN_FRAME_WIDTH = width * 0.7;
const DEFAULT_SCAN_FRAME_HEIGHT = DEFAULT_SCAN_FRAME_WIDTH * 0.7;

const CameraBase: React.FC<CameraBaseProps> = ({
    onCodeScanned,
    onPhotoTaken,
    onClose,
    initialCameraPosition = 'back',
    initialFlashMode = 'auto',
    codeTypes = DEFAULT_CODE_TYPES,
    disablePhotoGallery = false,
    showCloseButton = true,
    scanFrameSize,
}) => {
    const { colors } = useTheme() as any;
    const insets = useSafeAreaInsets();
    const { hasPermission, requestPermission } = useCameraPermission();
    const navigation = useNavigation<any>();
    const route = useRoute<any>();

    // Camera state
    const [isActive, setIsActive] = useState(true);
    const [cameraPosition, setCameraPosition] = useState<'back' | 'front'>(initialCameraPosition);
    const [flashMode, setFlashMode] = useState<'auto' | 'on' | 'off'>(initialFlashMode);
    const [zoomLevel, setZoomLevel] = useState(1);
    const [focusPoint, setFocusPoint] = useState<Point | null>(null);
    const [recentPhotos, setRecentPhotos] = useState<any[]>([]);
    const [cameraError, setCameraError] = useState<string | null>(null);
    const [photosTaken, setPhotosTaken] = useState<IosPhotoInfo[]>([]);//image[]
    const [showPhotoTaken, setShowPhotoTaken] = useState(false);
    const [isScanMode, setIsScanMode] = useState(false);
    const [photosTaked, setPhotosTaked] = useState<any[]>([]);

    // Scan-related state (only used in scan mode)
    const [scanHistory, setScanHistory] = useState<ScanResult[]>([]);
    const [lastScannedCode, setLastScannedCode] = useState<string | null>(null);

    // Camera setup
    const devices = Camera.getAvailableCameraDevices();
    const device = devices.find(d => d.position === cameraPosition);
    const format = useCameraFormat(device, [{ photoResolution: { width: 1280, height: 720 } }]);

    // Refs
    const camera = useRef<Camera | any>(null);

    // Animation values
    const scale = useSharedValue(1);
    const savedScale = useSharedValue(1);

    // Frame size for scan mode
    const SCAN_FRAME_WIDTH = scanFrameSize?.width || DEFAULT_SCAN_FRAME_WIDTH;
    const SCAN_FRAME_HEIGHT = scanFrameSize?.height || DEFAULT_SCAN_FRAME_HEIGHT;

    // Bouncing line animation for scan mode
    const animatedValue = useRef(new RNAnimated.Value(0)).current;

    // Add useFocusEffect to control camera based on screen focus
    useFocusEffect(
        useCallback(() => {
            // Screen is focused - start camera
            setIsActive(true);

            return () => {
                // Screen is unfocused - stop camera
                setIsActive(false);
                if (camera.current) {
                    camera.current = null;
                }
            };
        }, [])
    );

    // Create the bouncing animation for scanner
    useEffect(() => {
        if (isScanMode) {
            const startAnimation = () => {
                RNAnimated.loop(
                    RNAnimated.sequence([
                        RNAnimated.timing(animatedValue, {
                            toValue: 1,
                            duration: 1500,
                            useNativeDriver: true,
                        }),
                        RNAnimated.timing(animatedValue, {
                            toValue: 0,
                            duration: 1500,
                            useNativeDriver: true,
                        }),
                    ])
                ).start();
            };

            startAnimation();

            return () => {
                animatedValue.stopAnimation();
            };
        }
    }, [animatedValue, isScanMode]);

    useEffect(() => {

        if (route?.params?.isScan) {
            setIsScanMode(true);
        } else {
            setIsScanMode(false);
        }

        if (route?.params?.photosTaken) {
            setPhotosTaked(route?.params?.photosTaken);
        }

    }, [route]);

    const requestCameraPermission = async () => {
        try {
            const cameraPermission = await requestPermission();
            console.log('cameraPermission', cameraPermission);

            if (!cameraPermission) {
                Alert.alert(
                    'Camera Permission Required',
                    'Please grant camera access in settings to continue.',
                    [
                        { text: 'Cancel', style: 'cancel' },
                        {
                            text: 'Open Settings',
                            onPress: () => {
                                if (Platform.OS === 'ios') {
                                    Linking.openURL('app-settings:');
                                } else {
                                    openSettings();
                                }
                            },
                        },
                    ],
                    { cancelable: true }
                );
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error checking permissions:', error);
            Alert.alert('Permission Error', 'Unable to check camera permissions. Please try again.');
            return false;
        }
    };

    const goBack = () => {
        if (navigation && navigation.canGoBack()) {
            navigation.goBack()
        }
    }

    // Open device settings
    const openSettings = async () => {
        try {
            const { Linking } = require('react-native');
            await Linking.openSettings();
        } catch (error) {
            console.error('Error opening settings:', error);
            Alert.alert('Error', 'Unable to open settings. Please grant permissions manually in device settings.');
        }
    };

    // Load recent photos for thumbnail
    const loadRecentPhotos = async () => {

        try {
            // setRecentPhotos(photosTaken[photosTaken.length - 1]);
            // console.log('[loadRecentPhotos] recentPhotos', recentPhotos);
        } catch (error) {
            console.error('Error loading photos:', error);
        }
    };

    // Effect to request permissions and load photos
    useEffect(() => {
        const setup = async () => {
            const hasPermissions = await requestCameraPermission();
            if (hasPermissions) {
                loadRecentPhotos();
            }
        };

        setup();
    }, [disablePhotoGallery]);

    // Effect to reload photos when storage permission is granted
    useEffect(() => {
        loadRecentPhotos();
    }, []);

    // Barcode scanning setup
    const handleCodeScanned = useCallback((codes: any[]) => {
        if (!isScanMode) return;

        if (codes.length > 0 && codes[0].value && codes[0].value !== lastScannedCode) {
            const scannedValue = codes[0].value;
            setLastScannedCode(scannedValue);

            // Add to scan history
            const newScanResult = {
                value: scannedValue,
                timestamp: Date.now()
            };

            setScanHistory(prev => [newScanResult, ...prev]);

            // Pass the scanned code to parent component if callback exists
            if (onCodeScanned) {
                onCodeScanned(scannedValue);
            }

            // Briefly pause scanning after successful scan
            setIsActive(false);
            setTimeout(() => {
                setIsActive(true);
                setLastScannedCode(null);
            }, 1500);
        }
    }, [lastScannedCode, onCodeScanned, isScanMode]);

    const codeScanner = useCodeScanner({
        codeTypes: codeTypes,
        onCodeScanned: handleCodeScanned
    });


    // Calculate animation position for scanner line
    const animatedLinePosition = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0, SCAN_FRAME_HEIGHT]
    });

    // Focus handling
    const handleFocus = (point: Point) => {
        if (camera.current && device?.supportsFocus) {
            try {
                camera.current.focus(point);
                setFocusPoint(point);
                setTimeout(() => setFocusPoint(null), 1000);
            } catch (error) {
                console.error('Error focusing:', error);
            }
        }
    };

    // Handle tap gesture for focus
    const tapGesture = Gesture.Tap().onEnd((event) => {
        const point: Point = { x: event.x, y: event.y };
        runOnJS(handleFocus)(point);
    });

    // Handle pinch gesture for zoom
    const updateCameraZoom = (zoom: number) => {
        if (camera.current) {
            camera.current.zoom = zoom;
            setZoomLevel(zoom);
        }
    };

    const updateSavedScale = () => {
        if (camera.current) {
            savedScale.value = camera.current.zoom || 1;
        }
    };

    const pinchGesture = Gesture.Pinch()
        .onUpdate((event) => {
            if (device && camera.current) {
                const newZoom = savedScale.value * event.scale;
                const clampedZoom = Math.max(
                    device.minZoom || 1,
                    Math.min(device.maxZoom || 10, newZoom)
                );
                runOnJS(updateCameraZoom)(clampedZoom);
                scale.value = event.scale;
            }
        })
        .onEnd(() => {
            scale.value = withTiming(1);
            if (device && camera.current) {
                runOnJS(updateSavedScale)();
            }
        });

    // Combine gestures
    const composedGesture = Gesture.Simultaneous(tapGesture, pinchGesture);

    // Switch camera position
    const toggleCamera = () => {
        setCameraPosition(prev => prev === 'back' ? 'front' : 'back');
    };

    // Toggle flash mode
    const toggleFlash = () => {
        if (!device?.hasFlash) {
            Alert.alert('Notice', 'This camera does not support flash.');
            return;
        }
        setFlashMode(prev => prev === 'auto' ? 'on' : prev === 'on' ? 'off' : 'auto');
    };

    const takePhoto = useCallback(async () => {
        if (!camera.current || !device) {
            Alert.alert(
                'Error',
                'Device is not available. Please check again.',
                [
                    { text: 'Close', style: 'cancel' },
                    { text: 'Open settings', onPress: openSettings },
                ]
            );
            return;
        }

        try {
            const photo = await camera.current.takePhoto({
                flash: device.hasFlash ? flashMode : 'off',
                enableAutoRedEyeReduction: true,
                enableShutterSound: Platform.OS === 'ios',
                qualityPrioritization: 'speed',
                skipMetadata: false,
            });

            const oldPath = photo.path;
            const timestamp = Date.now();
            const fileName = `ARC_${timestamp}.jpg`;
            const newPath = `${RNFS.TemporaryDirectoryPath}/${fileName}`;

            await RNFS.moveFile(oldPath, newPath);

            const uri = `file://${newPath}`;
            const updatedPhoto = { ...photo, path: newPath, uri };


            setPhotosTaken([updatedPhoto]);
            setRecentPhotos([updatedPhoto]);

        } catch (error) {
            console.error('Error capturing photo:', error);
            Alert.alert('Error', 'Unable to capture photo. Please try again.');
        }
    }, []);


    // Help button handler
    const handleHelpPress = () => {
        Alert.alert(
            'Scanning Help',
            'Position the barcode within the frame and hold steady. Make sure there is adequate lighting and the barcode is clearly visible.',
            [{ text: 'Got it' }]
        );
    };

    // Handle close button press
    const handleClose = async (selectedImages?: any) => {
        try {
            // Deactivate camera first
            setIsActive(false);

            if (onClose) {
                onClose();
            }
            const uniqueImages = selectedImages.filter((image: any) => !isImageDuplicate(image, photosTaken));

            // Then navigate
            navigation.navigate(SCREEN_NAMES.DETAIL_TASK, {
                task: mockTasks.records[0],
                photosFromCamera: selectedImages ? uniqueImages : photosTaked || []
            });

        } catch (error) {
            console.error('[CameraBase] Error navigating to detail task:', error);
        }
    };



    // Animated style for zoom text display
    const zoomText = useAnimatedStyle(() => ({
        opacity: scale.value !== 1 ? 1 : 0,
    }));

    if (!hasPermission) {
        return (
            <View style={[styles.container, { backgroundColor: colors.background }]}>
                <CText style={[styles.message, { color: colors.text }]}>
                    Camera not available or permission denied
                </CText>
                <TouchableOpacity
                    style={[styles.helpButton, { marginTop: 20 }]}
                    onPress={handleClose}
                >
                    <CText style={styles.helpButtonText}>Close</CText>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.helpButton, { marginTop: 10 }]}
                    onPress={() => {
                        if (Platform.OS === 'ios') {
                            Linking.openURL('app-settings:');
                        } else {
                            openSettings();
                        }
                    }}
                >
                    <CText style={styles.helpButtonText}>Request Permissions</CText>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: colors.background }]}>
            <GestureDetector gesture={composedGesture}>
                <Camera
                    ref={camera}
                    style={StyleSheet.absoluteFill}
                    device={device as any}
                    isActive={isActive}
                    codeScanner={isScanMode ? codeScanner : undefined}
                    enableZoomGesture={false}
                    photo={true}
                    format={format}
                    zoom={zoomLevel}
                    enableDepthData={false}
                    fps={30}
                />
            </GestureDetector>

            {/* Close button */}
            {showCloseButton && (
                <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => { handleClose(photosTaked) }}
                >
                    <CText style={styles.closeButtonText}>✕</CText>
                </TouchableOpacity>
            )}

            {/* Zoom level indicator */}
            <Animated.View style={[styles.zoomTextContainer, zoomText]}>
                <CText style={styles.zoomText}>{`${zoomLevel.toFixed(1)}x`}</CText>
            </Animated.View>

            {/* Focus indicator */}
            {focusPoint && (
                <View
                    style={[
                        styles.focusPoint,
                        { left: focusPoint.x - 25, top: focusPoint.y - 25 },
                    ]}
                />
            )}

            <View style={[styles.overlay, { paddingTop: insets.top + 20 }]}>
                {isScanMode && (
                    <>
                        {/* Top text for scan mode */}
                        <CText style={styles.scanText}>Scan QR code or barcode</CText>

                        {/* Scan frame with bouncing line */}
                        <View style={styles.scanFrameContainer}>
                            <View style={[styles.scanFrame, { width: SCAN_FRAME_WIDTH, height: SCAN_FRAME_HEIGHT }]}>
                                <RNAnimated.View
                                    style={[
                                        styles.scanLine,
                                        {
                                            transform: [{ translateY: animatedLinePosition }],
                                            backgroundColor: 'rgba(255, 255, 255, 0.5)'
                                        }
                                    ]}
                                />
                            </View>
                        </View>

                        {/* Help button */}
                        <TouchableOpacity
                            style={styles.helpButton}
                            onPress={handleHelpPress}
                        >
                            <CText style={styles.helpButtonText}>Need help scanning?</CText>
                        </TouchableOpacity>

                        {/* Scan history */}
                        <View style={styles.historyContainer}>
                            <CText style={styles.historyTitle}>Scan history</CText>
                            {scanHistory.length === 0 ? (
                                <View style={styles.emptyHistoryContainer}>
                                    <CText style={styles.emptyHistoryText}>
                                        Your scanner history is empty.
                                    </CText>
                                    <CText style={styles.emptyHistorySubText}>
                                        Time to get started!
                                    </CText>
                                </View>
                            ) : (
                                <View style={styles.historyList}>
                                    {scanHistory.slice(0, 3).map((scan, index) => (
                                        <View key={index} style={styles.historyItem}>
                                            <CText style={styles.historyItemText} numberOfLines={1}>
                                                {scan.value}
                                            </CText>
                                            <CText style={styles.historyItemTime}>
                                                {new Date(scan.timestamp).toLocaleTimeString()}
                                            </CText>
                                        </View>
                                    ))}
                                </View>
                            )}
                        </View>
                    </>
                )}

                {!isScanMode && (
                    <>
                        {/* Flash toggle button */}
                        <TouchableOpacity style={styles.flashButton} onPress={toggleFlash}>
                            <CText style={styles.iconText}>
                                {flashMode === 'off' ? '⚡ Off' : flashMode === 'on' ? '⚡ On' : '⚡ Auto'}
                            </CText>
                        </TouchableOpacity>

                        {/* Camera controls for photo mode */}
                        <View style={styles.controlContainer}>
                            <View style={styles.functionContainer}>
                                <TouchableOpacity
                                    style={styles.thumbnailContainer}
                                    onPress={() => { setShowPhotoTaken(true) }}
                                >
                                    {recentPhotos[0]?.path ? (
                                        <Image
                                            source={{ uri: !isIOS ? recentPhotos[0]?.uri : recentPhotos[0]?.path }}
                                            style={styles.thumbnail}
                                        />
                                    ) : (
                                        <View style={{ alignItems: 'center', justifyContent: 'center', flex: 1 }}>
                                            <CText style={styles.iconText} size='size40'>🖼️</CText>
                                        </View>
                                    )}
                                </TouchableOpacity>

                            </View>

                            <View style={styles.functionContainer}>
                                <TouchableOpacity onPress={takePhoto} style={styles.captureButton}>
                                    <View style={styles.captureInner} />
                                </TouchableOpacity>
                            </View>

                            <View style={styles.functionContainer}>
                                <TouchableOpacity onPress={toggleCamera} style={styles.cameraToggleButton}>
                                    <CText style={styles.iconText}>🔄</CText>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </>
                )}

                <PhotoLibraryModal
                    visible={showPhotoTaken}
                    onClose={() => setShowPhotoTaken(false)}
                    onSelect={(selectedImages) => {
                        setShowPhotoTaken(false)
                        handleClose(selectedImages as any)
                    }}
                    multiple={true}
                    photosTaken={photosTaked}
                />


            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#000',
    },
    overlay: {
        flex: 1,
        alignItems: 'center',
    },
    scanText: {
        color: 'white',
        fontSize: 16,
        marginBottom: 20,
        fontWeight: FONT_WEIGHT.medium,
    },
    scanFrameContainer: {
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: 40,
    },
    scanFrame: {
        borderWidth: 2,
        borderColor: 'white',
        backgroundColor: 'transparent',
        position: 'relative',
        overflow: 'hidden',
    },
    scanLine: {
        height: 2,
        width: '100%',
        position: 'absolute',
    },
    helpButton: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 20,
        marginTop: 20,
    },
    helpButtonText: {
        color: '#333',
        fontSize: 14,
        fontWeight: FONT_WEIGHT.medium,
    },
    closeButton: {
        position: 'absolute',
        top: 50,
        right: 20,
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10,
    },
    closeButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: FONT_WEIGHT.bold,
    },
    historyContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(20, 20, 28, 0.9)',
        padding: 16,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
    },
    historyTitle: {
        color: 'white',
        fontSize: 18,
        fontWeight: FONT_WEIGHT.bold,
        marginBottom: 10,
    },
    historyList: {
        marginTop: 8,
    },
    historyItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
        paddingBottom: 8,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(255,255,255,0.1)',
    },
    historyItemText: {
        color: 'white',
        fontSize: 14,
        flex: 1,
        marginRight: 10,
    },
    historyItemTime: {
        color: 'rgba(255,255,255,0.5)',
        fontSize: 12,
    },
    emptyHistoryContainer: {
        alignItems: 'center',
        paddingVertical: 20,
    },
    emptyHistoryText: {
        color: 'white',
        fontSize: 14,
        opacity: 0.7,
    },
    emptyHistorySubText: {
        color: 'white',
        fontSize: 14,
        opacity: 0.7,
    },
    message: {
        fontSize: 16,
        textAlign: 'center',
        marginTop: 100,
    },
    zoomTextContainer: {
        position: 'absolute',
        top: 100,
        right: 20,
        backgroundColor: 'rgba(0,0,0,0.6)',
        padding: 8,
        borderRadius: 20,
        zIndex: 10,
    },
    zoomText: {
        color: 'white',
        fontSize: 14,
        fontWeight: FONT_WEIGHT.medium,
    },
    focusPoint: {
        width: 50,
        height: 50,
        borderRadius: 25,
        borderWidth: 2,
        borderColor: 'white',
        position: 'absolute',
    },
    controlContainer: {
        position: 'absolute',
        bottom: 40,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
    },
    functionContainer: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    captureButton: {
        width: 70,
        height: 70,
        borderRadius: 35,
        backgroundColor: 'rgba(255,255,255,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    captureInner: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: 'white',
    },
    cameraToggleButton: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'rgba(0,0,0,0.5)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    flashButton: {
        position: 'absolute',
        top: 50,
        left: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        padding: 10,
        borderRadius: 20,
        zIndex: 10,
    },
    iconText: {
        color: 'white',
        fontSize: 16,
    },
    thumbnailContainer: {
        width: 50,
        height: 50,
        borderRadius: 10,
        overflow: 'hidden',
        borderWidth: 2,
        borderColor: 'white',
    },
    thumbnail: {
        width: '100%',
        height: '100%',
    },
});

export default CameraBase; 