import React from 'react';
import {
    Text,
    TextProps,
    TouchableOpacity,
    GestureResponderEvent,
    ViewStyle,
    TextStyle,
} from 'react-native';
import { useTheme } from '@react-navigation/native';
import { BORDER_RADIUS, BUTTON_HEIGHT, FONT_FAMILY, FONT_SIZE, FONT_WEIGHT, SPACING } from '@/constants/UI/themes';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';
import CText from './CText';
import { adjustHeight, adjustSpacing } from '@/constants/UI/responsive';

interface CButtonProps extends TextProps {
    children: React.ReactNode;
    weight?: keyof typeof FONT_WEIGHT;
    size?: keyof typeof FONT_SIZE;
    onPress?: (event: GestureResponderEvent) => void;
    style?: ViewStyle;
    textStyle?: TextStyle;
}

const CButton: React.FC<CButtonProps> = ({
    children,
    weight = 'semiBold',
    size = 'lg',
    onPress,
    style,
    textStyle,
    ...props
}) => {

    const { colors } = useTheme();

    return (
        <TouchableOpacity
            onPress={onPress}
            activeOpacity={0.8}
            style={[
                {
                    ...BASE_STYLE.centerContent,
                    height: adjustHeight(BUTTON_HEIGHT),
                    paddingHorizontal: adjustSpacing(SPACING.md),
                    borderRadius: BORDER_RADIUS.sm,
                    backgroundColor: colors.primary,
                },
                style,
            ]}
        >
            <CText
                style={[
                    {
                        color: colors.text,
                        fontFamily: FONT_FAMILY["bold"],
                        fontSize: FONT_SIZE[size],
                    },
                    textStyle,
                ]}
                {...props}
            >
                {children}
            </CText>
        </TouchableOpacity>
    );
};

export default CButton;

