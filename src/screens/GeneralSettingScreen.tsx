import React from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    Alert,
    Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useTheme } from '@react-navigation/native';
import { ParamListBase } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SPACING, COLORS, BORDER_RADIUS, STATUS_BAR, GRADIENT_VALUE } from '@/constants/UI/themes';
import CIcon from '@/components/CIcon';
import { Iback, Iarrow, Isetting, Iprivacy } from '@/constants/UI/icons';
import { useTranslation } from 'react-i18next';
import { APP_INFO } from '@/constants/app';
import { SCREEN_NAMES, PRIVACY_POLICY_URL } from '@/constants/navigation';
import { Linking } from 'react-native';
import { AppTheme } from '@/types';
import LinearGradient from 'react-native-linear-gradient';
import { ADMIN_EMAIL } from '@/constants/contact';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';

type GeneralSettingScreenNavigationProp = NativeStackNavigationProp<ParamListBase>;

const GeneralSettingScreen = () => {
    const navigation = useNavigation<GeneralSettingScreenNavigationProp>();
    const { colors } = useTheme() as unknown as AppTheme;
    const { t } = useTranslation();
    const { data } = useSelector((state: RootState) => state.user);
    const handleBack = () => {
        navigation.goBack();
    };
    const handlePrivacyPolicy = () => {
        Linking.openURL(PRIVACY_POLICY_URL);
    };

    const handleContactAdmin = async () => {
        const emailParams = {
            to: ADMIN_EMAIL,
            subject: 'Support Request',
            from: data?.email
        };

        // URL schemes cho các nền tảng khác nhau
        const androidGmailSchemes = [
            'com.google.android.gm://',  // Gmail package name
            'gmail://',                  // Gmail alternative scheme
            'googlegmail://'             // Another Gmail scheme
        ];

        const isAndroid = Platform.OS === 'android';

        if (isAndroid) {
            // Thử các scheme Gmail trên Android
            for (const scheme of androidGmailSchemes) {
                try {
                    const gmailUrl = `${scheme}compose?to=${emailParams.to}&subject=${encodeURIComponent(emailParams.subject)}`;
                    const canOpen = await Linking.canOpenURL(gmailUrl);
                    if (canOpen) {
                        await Linking.openURL(gmailUrl);
                        return;
                    }
                } catch (error) {
                    console.log(`Failed to open with scheme ${scheme}:`, error);
                }
            }

            // Nếu không mở được bằng scheme, thử dùng Intent
            try {
                const intentUrl = `mailto:${emailParams.to}?subject=${encodeURIComponent(emailParams.subject)}`;
                await Linking.openURL(intentUrl);
                return;
            } catch (error) {
                console.log('Failed to open with intent:', error);
            }
        } else {
            // Xử lý cho iOS
            try {
                const mailtoUrl = `mailto:${emailParams.to}?subject=${encodeURIComponent(emailParams.subject)}`;
                const canOpenMailto = await Linking.canOpenURL(mailtoUrl);

                if (canOpenMailto) {
                    await Linking.openURL(mailtoUrl);
                    return;
                }
            } catch (error) {
                console.log('Failed to open mail client:', error);
            }
        }

        // Fallback: Mở trong trình duyệt web
        try {
            const fallbackUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${emailParams.to}&subject=${encodeURIComponent(emailParams.subject)}`;
            await Linking.openURL(fallbackUrl);
        } catch (error) {
            console.error('Error opening web browser:', error);
            Alert.alert(
                'Error',
                'Could not open any email client. Please try again later.',
                [{ text: 'OK' }]
            );
        }
    };

    return (

        <SafeAreaView style={styles.container} edges={['left', 'right']}>
            <LinearGradient
                colors={colors.linearGradient}
                locations={GRADIENT_VALUE}
                style={styles.container}
            >
                <ScrollView style={styles.scrollView}>
                    {/* Menu Items */}
                    <View style={styles.menuSection}>
                        {/* Privacy Policy */}
                        <TouchableOpacity
                            style={styles.menuItem}
                            onPress={handlePrivacyPolicy}
                        >
                            <View style={styles.menuIconContainer}>
                                <CIcon source={Iprivacy} size={30} tintColor={COLORS.light.black} />
                            </View>
                            <Text style={styles.menuText}>Privacy Policy</Text>
                            <CIcon source={Iarrow} size={20} tintColor={COLORS.light.black} style={styles.arrowIcon} />
                        </TouchableOpacity>

                        {/* Contact Admin */}
                        <TouchableOpacity
                            style={styles.menuItem}
                            onPress={handleContactAdmin}
                        >
                            <View style={styles.menuIconContainer}>
                                <CIcon source={Isetting} size={25} tintColor={COLORS.light.black} />
                            </View>
                            <Text style={styles.menuText}>Contact Support</Text>
                            <CIcon source={Iarrow} size={24} tintColor={COLORS.light.black} style={styles.arrowIcon} />
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </LinearGradient>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.light.background,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        backgroundColor: COLORS.light.white,
        borderBottomWidth: 1,
        borderBottomColor: COLORS.light.border,
    },
    backButton: {
        padding: SPACING.xs,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.light.black,
    },
    emptySpace: {
        width: 24,
    },
    scrollView: {
        flex: 1,
    },
    menuSection: {
        backgroundColor: COLORS.light.white,
        marginTop: SPACING.md,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: SPACING.md,
        paddingHorizontal: SPACING.md,
        borderBottomWidth: 1,
        borderBottomColor: COLORS.light.border,
    },
    menuIconContainer: {
        marginRight: SPACING.md,
    },
    menuText: {
        flex: 1,
        fontSize: 16,
        color: COLORS.light.text,
    },
    arrowIcon: {
        transform: [{ rotate: '90deg' }],
    },
    versionContainer: {
        alignItems: 'center',
        padding: SPACING.md,
    },
    versionText: {
        fontSize: 14,
        color: COLORS.light.textSecondary,
        marginBottom: SPACING.xs,
    },
    versionLine: {
        width: 50,
        height: 4,
        backgroundColor: COLORS.light.black,
        borderRadius: BORDER_RADIUS.round,
    },
});

export default GeneralSettingScreen; 