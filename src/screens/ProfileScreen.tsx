import React, { useState, useEffect } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect, useNavigation, useTheme } from '@react-navigation/native';
import { ParamListBase } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SPACING, COLORS, BORDER_RADIUS, GRADIENT_VALUE, FONT_WEIGHT, FONT_SIZE, INPUT_HEIGHT } from '@/constants/UI/themes';
import CIcon from '@/components/CIcon';
import { Iback, Icamera, Ichange } from '@/constants/UI/icons';
import ProfileAvatar from '@/components/ProfileAvatar';
import CInput from '@/components/CInput';
import { useBottomSheet } from '@/hocs/BottomSheetContext';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import LinearGradient from 'react-native-linear-gradient';
import { AppTheme } from '@/types';
import { SCREEN_NAMES } from '@/constants/navigation';
import { HeaderBackButton } from '@react-navigation/elements';
import { DEFAULT_USER_ROLE } from '@/constants/app';
import { DEFAULT_USER_NAME } from '@/constants/app';
import { DEFAULT_AVATAR_URL } from '@/constants/app';
import ProfileSection from '@/components/ProfileSection';
import { adjustHeight, getResponsiveFontSize } from '@/constants/UI/responsive';

type ProfileScreenNavigationProp = NativeStackNavigationProp<ParamListBase>;

const ProfileScreen = () => {
    const { colors } = useTheme() as unknown as AppTheme;
    const navigation = useNavigation<ProfileScreenNavigationProp>();
    const { openBottomSheet, closeBottomSheet } = useBottomSheet();
    const { user } = useSelector((state: RootState) => state);
    const [userName, setUserName] = useState('');
    const [email, setEmail] = useState('');
    const [phoneNumber, setPhoneNumber] = useState('');
    const { data } = useSelector((state: RootState) => state.user);

    useFocusEffect(
        React.useCallback(() => {
            navigation.setOptions({
                headerLeft: () => (
                    <HeaderBackButton
                        onPress={() => navigation.navigate(SCREEN_NAMES.SETTINGS)}
                        tintColor="#000"
                    />
                ),
                // swipeEnabled: false
            });
        }, [navigation])
    );
    // Initialize form with user data
    useEffect(() => {
        if (user) {
            setUserName(user.data?.name || '');
            setEmail(user.data?.email || '');
            // Use phoneNumber from user object if available, or empty string
            setPhoneNumber(user.data?.phone || '');
        }
    }, [user]);

    // Handle password change
    const handleChangePassword = () => {
        navigation.navigate('ChangePassword');
    };


    return (
        <SafeAreaView style={styles.container} edges={['left', 'right']}>
            <LinearGradient
                colors={colors.linearGradient}
                locations={GRADIENT_VALUE}
                style={styles.container}
            >
                <ScrollView style={styles.scrollView}>
                    {/* Profile Avatar Section */}
                    <ProfileSection data={data} />
                    {/* Profile Form */}
                    <View style={styles.formContainer}>
                        {/* Email */}
                        <View style={styles.inputGroup}>
                            <Text style={styles.inputLabel}>Email</Text>
                            <CInput
                                editable={false}
                                value={email}
                                onChangeText={setEmail}
                                style={styles.input}
                                contentStyle={styles.inputContent}
                                keyboardType="email-address"
                                autoCapitalize="none"
                            />
                        </View>

                        {/* Phone Number */}
                        <View style={styles.inputGroup}>
                            <Text style={styles.inputLabel}>Phone Number</Text>
                            <CInput
                                editable={false}
                                value={phoneNumber}
                                onChangeText={setPhoneNumber}
                                style={styles.input}
                                contentStyle={styles.inputContent}
                                keyboardType="phone-pad"
                            />
                        </View>

                        {/* Change Password Button */}
                        <TouchableOpacity
                            style={styles.changePasswordButton}
                            onPress={handleChangePassword}
                        >
                            <Text style={styles.changePasswordText}>Change Password</Text>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </LinearGradient>


        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5faff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        backgroundColor: COLORS.light.white,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    backButton: {
        padding: SPACING.xs,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    emptySpace: {
        width: 24,
    },
    scrollView: {
        flex: 1,
    },
    avatarSection: {
        alignItems: 'center',
        padding: SPACING.md,
        // backgroundColor: '#fff',
    },
    settingIcon: {
        position: 'absolute',
        top: SPACING.lg,
        right: SPACING.md,
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: SPACING.sm,
    },
    avatar: {
        width: 64,
        height: 64,
        borderRadius: 32,
        borderWidth: 1,
        borderColor: COLORS.light.black,
    },
    cameraButton: {
        position: 'absolute',
        bottom: 0,
        right: 0,
        backgroundColor: COLORS.light.primary,
        width: 32,
        height: 32,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 2,
        borderColor: '#fff',
    },

    profileSection: {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        // backgroundColor: COLORS.light.white,
        paddingVertical: SPACING.lg,
        paddingHorizontal: SPACING.md,
    },

    profileInfo: {
        flex: 1,
        alignItems: 'center',

    },
    profileName: {
        fontSize: 18,
        fontWeight: FONT_WEIGHT.semiBold,
        color: COLORS.light.primary,
        marginTop: SPACING.sm,
    },
    profileRole: {
        fontSize: 14,
        color: COLORS.light.textSecondary,
        marginTop: 2,
    },
    formContainer: {
        // backgroundColor: '#fff',
        padding: SPACING.md,
        marginTop: SPACING.md,
    },
    inputGroup: {
        marginBottom: SPACING.md,
    },
    inputLabel: {
        fontSize: getResponsiveFontSize(FONT_SIZE.lg),
        marginBottom: SPACING.xs,
    },
    input: {
        height: adjustHeight(INPUT_HEIGHT),
        borderWidth: 1,
        borderColor: '#e0e0e0',
        borderRadius: BORDER_RADIUS.sm,
    },
    inputContent: {
        paddingHorizontal: SPACING.md,
        color: COLORS.light.placeholder,
        fontSize: getResponsiveFontSize(FONT_SIZE.md),
    },
    changePasswordButton: {
        borderWidth: 1,
        borderColor: COLORS.light.primary,
        borderRadius: BORDER_RADIUS.md,
        padding: SPACING.md,
        alignItems: 'center',
        marginVertical: SPACING.md,
    },
    changePasswordText: {
        color: COLORS.light.primary,
        fontSize: getResponsiveFontSize(FONT_SIZE.size20),
        fontWeight: FONT_WEIGHT.medium,
    },
    avatarButtons: {
        marginTop: SPACING.md,
    },
    viewAvatarButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: SPACING.sm,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    updateAvatarButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: SPACING.sm,
    },
    avatarButtonText: {
        marginLeft: SPACING.sm,
        fontSize: 16,
        color: COLORS.light.primary,
    },
    versionContainer: {
        alignItems: 'center',
        padding: SPACING.md,
    },
    versionText: {
        fontSize: 14,
        color: '#666',
        marginBottom: SPACING.xs,
    },
    versionLine: {
        width: 50,
        height: 4,
        backgroundColor: '#000',
        borderRadius: BORDER_RADIUS.round,
    },
    bottomSheetContent: {
        width: '100%',
        alignItems: 'center',
        paddingVertical: SPACING.md,
    },
    bottomSheetTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: SPACING.md,
    },
    largeAvatarContainer: {
        marginVertical: SPACING.lg,
    },
    largeAvatar: {
        width: 150,
        height: 150,
        borderRadius: 75,
    },
    closeButton: {
        borderWidth: 1,
        borderColor: COLORS.light.primary,
        borderRadius: BORDER_RADIUS.md,
        padding: SPACING.md,
        alignItems: 'center',
        width: '80%',
        marginTop: SPACING.lg,
    },
    closeButtonText: {
        color: COLORS.light.primary,
        fontSize: 16,
        fontWeight: '500',
    },
});

export default ProfileScreen; 