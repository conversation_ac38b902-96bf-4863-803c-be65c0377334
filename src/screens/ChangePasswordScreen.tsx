import React from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    Keyboard,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    TouchableWithoutFeedback
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CommonActions, useFocusEffect, useNavigation, useTheme } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ParamListBase } from '@react-navigation/native';
import { SPACING, COLORS, BORDER_RADIUS, GRADIENT_VALUE, FONT_SIZE, FONT_WEIGHT } from '@/constants/UI/themes';
import PasswordInput from '@/components/PasswordInput';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@/redux/store';
import { useToast } from '@/hocs/toast';
import { changePassword } from '@/redux/slices/userSlice';
import i18n from '@/i18n/i18n';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { AppTheme, ChangePasswordFormData } from '@/types';
import { changePasswordSchema } from '@/constants/validationSchema';
import { useLoading } from '@/hocs/LoadingContext';
import LinearGradient from 'react-native-linear-gradient';
import { adjustSpacing, getResponsiveFontSize } from '@/constants/UI/responsive';
import { HeaderBackButton } from '@react-navigation/elements';
import { SCREEN_NAMES } from '@/constants/navigation';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import { removeExtraSpaces } from '@/utils/text';
import { checkSession } from '@/redux/slices/authSlice';

type ChangePasswordScreenNavigationProp = NativeStackNavigationProp<ParamListBase>;


const ChangePasswordScreen = () => {
    const navigation = useNavigation<ChangePasswordScreenNavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    const { showToast } = useToast();
    const { show, hide, withLoading } = useLoading();
    const { control, handleSubmit, formState: { errors }, reset } = useForm<ChangePasswordFormData>({
        resolver: yupResolver(changePasswordSchema),
        defaultValues: {
            newPassword: '',
            currentPassword: '',
            confirmPassword: ''
        }
    });
    const { colors } = useTheme() as unknown as AppTheme;


    useFocusEffect(
        React.useCallback(() => {
            navigation.setOptions({
                headerLeft: () => (
                    <HeaderBackButton
                        onPress={() => navigation.navigate(SCREEN_NAMES.PROFILE)}
                        tintColor="#000"
                    />
                ),
                // swipeEnabled: false
            });
        }, [navigation])
    );

    const onSubmit = async (data: ChangePasswordFormData) => {
        Keyboard.dismiss();
        show();
        try {
            const response = await dispatch(changePassword({
                currentPassword: data.currentPassword,
                newPassword: data.newPassword,
                confirmPassword: data.confirmPassword
            })).unwrap();

            if (response.success) {
                showToast(i18n.t(response.data.message ? response.data.message : 'auth.passwordChanged'), 'success');
                reset();

                dispatch(checkSession());
            } else {
                showToast(response.message, 'error');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : i18n.t('errors.updateChangePasswordFailed');
            showToast(errorMessage, 'error');
        } finally {
            hide();
        }
    };

    const handleTextChange = (_field: string, text: string, onChange: (value: string) => void) => {

        switch (_field) {
            case 'currentPassword':
            case 'newPassword':
            case 'confirmPassword':
                onChange(removeExtraSpaces(text));
                break;
            default:
                onChange(text);
        }
    };

    const handleBlur = (_field: string, value: string, onChange: (value: string) => void, onBlur?: () => void) => {

        switch (_field) {
            case 'currentPassword':
            case 'newPassword':
            case 'confirmPassword':
                onChange(removeExtraSpaces(value || ''));
                break;
        }
        if (onBlur) onBlur();
    };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <SafeAreaView style={styles.container} edges={['left', 'right']}>
                <KeyboardAvoidingView
                    style={{ flex: 1 }}
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    keyboardVerticalOffset={0}
                >
                    <LinearGradient
                        colors={colors.linearGradient}
                        locations={GRADIENT_VALUE}
                        style={styles.container}
                    >
                        <ScrollView
                            contentContainerStyle={styles.scrollContainer}
                            keyboardShouldPersistTaps="handled"
                            showsVerticalScrollIndicator={false}
                        >
                            <View style={styles.formContainer}>
                                <Controller
                                    control={control}
                                    name="currentPassword"
                                    render={({ field: { onChange, value } }: { field: { onChange: (text: string) => void; value: string } }) => (
                                        <PasswordInput
                                            label={i18n.t('fields.currentPassword.label')}
                                            value={value?.replace(/\s+/g, '')}
                                            onChangeText={(text) => handleTextChange('currentPassword', text, onChange)}
                                            placeholder={i18n.t('fields.currentPassword.placeholder')}
                                            error={errors.currentPassword?.message}
                                            onBlur={() => handleBlur('currentPassword', value, onChange)}
                                        />
                                    )}
                                />

                                <Controller
                                    control={control}
                                    name="newPassword"
                                    render={({ field: { onChange, value } }: { field: { onChange: (text: string) => void; value: string } }) => (
                                        <PasswordInput
                                            label={i18n.t('fields.newPassword.label')}
                                            value={value?.replace(/\s+/g, '')}
                                            onChangeText={(text) => handleTextChange('newPassword', text, onChange)}
                                            placeholder={i18n.t('fields.newPassword.placeholder')}
                                            error={errors.newPassword?.message}
                                            onBlur={() => handleBlur('newPassword', value, onChange)}
                                        />
                                    )}
                                />

                                <Controller
                                    control={control}
                                    name="confirmPassword"
                                    render={({ field: { onChange, value } }: { field: { onChange: (text: string) => void; value: string } }) => (
                                        <PasswordInput
                                            label={i18n.t('fields.confirmPassword.label')}
                                            value={value?.replace(/\s+/g, '')}
                                            onChangeText={(text) => handleTextChange('confirmPassword', text, onChange)}
                                            placeholder={i18n.t('fields.confirmPassword.placeholder')}
                                            error={errors.confirmPassword?.message}
                                            onBlur={() => handleBlur('confirmPassword', value, onChange)}
                                        />
                                    )}
                                />
                            </View>

                            <TouchableOpacity
                                style={[styles.updateButton, { marginBottom: adjustSpacing(50) }]}
                                onPress={handleSubmit(onSubmit)}
                            >
                                <Text style={styles.updateButtonText}>
                                    {i18n.t('common.update')}
                                </Text>
                            </TouchableOpacity>
                        </ScrollView>
                    </LinearGradient>
                </KeyboardAvoidingView>
            </SafeAreaView>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'space-between',
        paddingHorizontal: SPACING.md,
    },
    formContainer: {
        marginTop: SPACING.md,
    },
    updateButton: {
        // backgroundColor: COLORS.light.primary,
        borderWidth: 1,
        borderRadius: BORDER_RADIUS.md,
        padding: SPACING.md,
        alignItems: 'center',
        // marginTop: SPACING.md,
        borderColor: COLORS.light.primary,
        marginVertical: SPACING.md,
        width: '100%',
    },
    updateButtonText: {
        color: COLORS.light.primary,
        fontSize: getResponsiveFontSize(FONT_SIZE.size20),
        fontWeight: FONT_WEIGHT.semiBold,
    },
});

export default withResponsiveLayout(ChangePasswordScreen);