import { BASE_STYLE } from '@/constants/UI/responsiveStyles';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import React from 'react';
import { Text, View, } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

function ReportedAndHistoryScreen() {
    return (
        <View style={BASE_STYLE.centerContent}>
            <Text>Reported and History</Text>
        </View>
    );
}
export default withResponsiveLayout(ReportedAndHistoryScreen)