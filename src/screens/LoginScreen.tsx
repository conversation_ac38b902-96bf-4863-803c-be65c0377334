import React, { useState, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    Image,
    TextInput,
    ImageProps,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { AppDispatch, RootState } from '../redux/store';
import { loginUser } from '../redux/slices/authSlice';
import { Controller } from 'react-hook-form';
import withForm from '../hocs/withForm';
import LinearGradient from 'react-native-linear-gradient';
import { NavigationProp, useNavigation, useTheme } from '@react-navigation/native';
import { Text } from 'react-native-gesture-handler';
import { loginSchema } from '@/constants/validationSchema';
import CCheckbox from '@/components/CCheckbox';
import { Ieye, IeyeOff, Ilogo } from '@/constants/UI/icons';
import { AppTheme, LoginData, StackParamList } from '@/types';
import CInput from '@/components/CInput';
import { COLORS, GRADIENT_VALUE } from '@/constants/UI/themes';
import { useTranslation } from 'react-i18next';
import { createAuthStyles } from '@/styles/authStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SCREEN_NAMES } from '@/constants/navigation';
import { STORAGE_KEYS } from '@/constants/auth';
import { useToast } from '@/hocs/toast';
import { removeItem, storeString } from '@/utils/storageUtils';
import CIcon from '@/components/CIcon';
import { useLoading } from '@/hocs/LoadingContext';
import { DEFAULT_BASE_URL } from '@/constants/api';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import apiClient from '@/services/api/apiClient';
import { removeExtraSpaces } from '@/utils/text';


const LoginScreen = (props: any) => {
    const theme = useTheme() as unknown as AppTheme;
    const { colors } = theme;
    const { t } = useTranslation();
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<NavigationProp<StackParamList>>();
    const { loading } = useSelector(
        (state: RootState) => state.auth
    );
    const { handleSubmit, control, formState: { errors }, setValue } = props;
    // const [remember, setRemember] = useState(false);
    const [isSecure, setIsSecure] = useState(true);
    const styles = createAuthStyles(theme);
    const { showToast } = useToast();
    const { show, hide } = useLoading();
    const urlInputRef = React.useRef<TextInput>(null);
    // Load saved credentials on component mount
    useEffect(() => {
        const loadSavedCredentials = async () => {
            try {
                // const rememberedValue = await AsyncStorage.getItem(STORAGE_KEYS.REMEMBER_ME);
                // if (rememberedValue === 'true') {
                //     setRemember(true);
                // }
                const savedEmail = await AsyncStorage.getItem(STORAGE_KEYS.USER_EMAIL);
                const savedUrl = await AsyncStorage.getItem(STORAGE_KEYS.USER_URL);

                if (savedEmail) {
                    setValue('email', savedEmail);
                }

                if (savedUrl) {
                    setValue('url', savedUrl);
                }
            } catch (error) {
                console.error('Error loading saved credentials:', error);
            }
        };

        loadSavedCredentials();
    }, [setValue]);


    const onSubmit = async (data: LoginData) => {
        try {
            show();
            await apiClient.setBaseUrl(data.url);

            dispatch(loginUser({
                email: data.email,
                password: data.password,
                url: data.url
            })).unwrap()
                .then(async (result) => {

                    if (result.success) {
                        // Save email và url lasted
                        await AsyncStorage.setItem(STORAGE_KEYS.USER_EMAIL, data.email);
                        if (data.url) {
                            await AsyncStorage.setItem(STORAGE_KEYS.USER_URL, data.url);
                        }
                        showToast(result.message, 'success');
                    }
                    hide();
                })///
                .catch((error) => {
                    console.log('error', error)
                    showToast(error as string ?? "Something went wrong", 'error');
                    hide();
                });

            // Save credentials if remember me is checked
            // if (remember) {
            //     storeString(STORAGE_KEYS.REMEMBER_ME, 'true');
            //     storeString(STORAGE_KEYS.USER_EMAIL, data.email);
            //     if (data.url) {
            //         storeString(STORAGE_KEYS.USER_URL, data.url);
            //     }
            // } else {
            //     // Clear saved credentials if remember me is unchecked
            //     removeItem(STORAGE_KEYS.REMEMBER_ME);
            // }
        } catch (err) {
            console.error('Login dispatch error:', err);
        }
    };

    // const onToggleRemember = () => {
    //     setRemember(!remember);
    // };

    const goToForgetPasswordScreen = () => {
        navigation.navigate(SCREEN_NAMES.FORGOT_PASSWORD as keyof StackParamList);
    };

    const renderForm = () => {
        const fields = ["url", "email", "password"];

        const handleTextChange = (_field: string, text: string, onChange: (value: string) => void) => {

            switch (_field) {
                case 'url':
                    onChange(`https://${removeExtraSpaces(text.trim().toLowerCase())}`);
                    break;
                case 'email':
                    onChange(removeExtraSpaces(text.trim().toLowerCase()));
                    break;
                case 'password':
                    onChange(removeExtraSpaces(text));
                    break;
                default:
                    onChange(text);
            }
        };

        const handleBlur = (_field: string, value: string, onChange: (value: string) => void, onBlur?: () => void) => {
            const removeExtraSpaces = (str: string) => str.replace(/\s+/g, '');

            switch (_field) {
                case 'url':
                    if (!value || value === 'https://') {
                        onChange('');
                    } else {
                        onChange(`https://${removeExtraSpaces(value.replace('https://', '').trim().toLowerCase())}`);
                    }
                    break;
                case 'email':
                    onChange(removeExtraSpaces(value?.trim()?.toLowerCase() || ''));
                    break;
                case 'password':
                    onChange(removeExtraSpaces(value || ''));
                    break;
            }
            if (onBlur) onBlur();
        };

        return (
            <View style={styles.formContainer}>
                <View>
                    {fields.map((_field) => (
                        <View style={styles.fieldContainer} key={_field}>
                            <View style={styles.labelContainer}>
                                <Text style={styles.label}>
                                    {_field === 'url' ? t('Website') : t(`fields.${_field}.label`)}
                                </Text>
                                <Text style={styles.required}>*</Text>
                            </View>
                            <Controller
                                control={control}
                                name={_field}
                                render={({ field: { onChange, value, onBlur } }) => (
                                    <View>
                                        {_field == 'url' && (
                                            <View style={styles.urlContainer}>
                                                <Text
                                                    style={styles.urlPrefix}
                                                    onPress={() => urlInputRef.current?.focus()}
                                                >
                                                    https://
                                                </Text>
                                                <TextInput
                                                    ref={urlInputRef}
                                                    placeholder={t(`fields.url.placeholder`)}
                                                    value={value?.replace('https://', '')?.trim()?.toLowerCase()}
                                                    onChangeText={(text) => handleTextChange(_field, text, onChange)}
                                                    onBlur={() => handleBlur(_field, value, onChange, onBlur)}
                                                    style={styles.urlInput}
                                                    placeholderTextColor={'#555'}
                                                />
                                            </View>
                                        )}
                                        {_field == 'email' && (
                                            <CInput
                                                value={value?.trim()?.toLowerCase()}
                                                onChangeText={(text) => handleTextChange(_field, text, onChange)}
                                                onBlur={() => handleBlur(_field, value, onChange, onBlur)}
                                                style={styles.input}
                                                placeholder={t('fields.email.placeholder')}
                                                keyboardType="email-address"
                                                autoCapitalize="none"
                                            />
                                        )}
                                        {_field == 'password' && (
                                            <View style={styles.passwordContainer}>
                                                <CInput
                                                    value={value?.trim()}
                                                    onChangeText={(text) => handleTextChange(_field, text, onChange)}
                                                    onBlur={() => handleBlur(_field, value, onChange, onBlur)}
                                                    style={styles.input}
                                                    placeholder={t('fields.password.placeholder')}
                                                    secureTextEntry={isSecure}
                                                />
                                                <TouchableOpacity
                                                    style={styles.eyeIcon}
                                                    onPress={() => setIsSecure(!isSecure)}
                                                >
                                                    <CIcon
                                                        source={isSecure ? Ieye : IeyeOff}
                                                        size={20}
                                                        tintColor={COLORS.light.textSecondary}
                                                    />
                                                </TouchableOpacity>
                                            </View>
                                        )}
                                    </View>
                                )}
                            />
                            {errors[_field] && (
                                <Text style={styles.error}>{errors[_field]?.message as string}</Text>
                            )}
                        </View>
                    ))}

                    {/* <View style={styles.checkboxContainer}>
                        <CCheckbox
                            label={t('auth.rememberMe')}
                            value={remember}
                            onChange={onToggleRemember}
                        />
                    </View> */}
                </View>

                <View style={{ flexDirection: 'column', justifyContent: 'space-between' }}>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={!loading ? handleSubmit(onSubmit) : undefined}
                    >
                        <Text style={styles.actionButtonText}>{t('auth.signin')}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.linkButton}
                        onPress={goToForgetPasswordScreen}
                    >
                        <Text style={styles.linkButtonText}>{t('auth.forgotPassword')}?</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    return (
        <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -200}
            enabled
        >
            <LinearGradient
                colors={colors.backgroundGradient}
                locations={GRADIENT_VALUE}
                style={styles.container}
            >
                {Platform.OS === 'ios' ? (
                    <View style={{ flex: 1 }}>
                        <View style={[
                            styles.contentContainer,
                            { justifyContent: 'center' }
                        ]}>
                            <View style={styles.logoContainer}>
                                <Image source={Ilogo as ImageProps} style={styles.logo} />
                            </View>
                            {renderForm()}
                        </View>
                    </View>
                ) : (
                    <ScrollView
                        contentContainerStyle={styles.scrollContainer}
                        keyboardShouldPersistTaps="handled"
                        showsVerticalScrollIndicator={false}
                        bounces={false}
                        keyboardDismissMode="interactive"
                        overScrollMode="never"
                    >
                        <View style={[
                            styles.contentContainer,
                            { minHeight: '100%', justifyContent: 'center' }
                        ]}>
                            <View style={styles.logoContainer}>
                                <Image source={Ilogo as ImageProps} style={styles.logo} />
                            </View>
                            {renderForm()}
                        </View>
                    </ScrollView>
                )}
            </LinearGradient>
        </KeyboardAvoidingView>
    );
};

const EnhancedLoginScreen = withForm<FormData>(withResponsiveLayout(LoginScreen));

// Component wrapper to load saved URL as default value
const LoginScreenWrapper = () => {
    // const [defaultUrl, setDefaultUrl] = React.useState('');

    // React.useEffect(() => {
    //     const loadSavedUrl = async () => {
    //         try {
    //             const savedUrl = await AsyncStorage.getItem(STORAGE_KEYS.USER_URL);
    //             if (savedUrl) {
    //                 setDefaultUrl(savedUrl);
    //             }
    //         } catch (error) {
    //             console.error('Error loading saved URL for default:', error);
    //         }
    //     };

    //     loadSavedUrl();
    // }, []);

    return (
        <EnhancedLoginScreen
            defaultValues={{
                url: '',
                email: '',
                password: '',
            }}
            schema={loginSchema}
            onSubmit={() => { }}
        />
    );
};

export default LoginScreenWrapper;