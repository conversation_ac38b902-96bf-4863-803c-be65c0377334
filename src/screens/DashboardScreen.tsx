import {
    PanResponder,
    Animated,
    Easing,
    TouchableWithoutFeedback,
    Dimensions,
} from 'react-native';
import { NavigationProp, useNavigation, useTheme } from '@react-navigation/native';
import React, { useState, useEffect, useRef } from 'react';
import {
    Text,
    StyleSheet,
    View, TouchableOpacity, Modal, FlatList, Pressable
} from 'react-native';
import { useTranslation } from 'react-i18next';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import { AppTheme, DrawerParamList } from '@/types';
import { TaskList } from '@/components/TaskList';
import { SPACING, COLORS, ICON_SIZE, GRADIENT_VALUE, ZINDEX } from '@/constants/UI/themes';
import { Calendar2 } from '@/components/Calendar_v2';
import { DateHeader } from '@/components/DateHeader';
import { WeekDays } from '@/components/WeekDays';
import { Iarrow, Isetting, Imap } from '@/constants/UI/icons';
import { mockTasks } from '@/constants/mock/tasks';
import { TaskListData } from '@/interfaces/task';
import CIcon from '@/components/CIcon';
import LinearGradient from 'react-native-linear-gradient';
import { getResponsiveFontSize } from '@/constants/UI/responsive';


function DashboardScreen(props: any) {
    const navigation = useNavigation<NavigationProp<DrawerParamList>>();
    const [refreshing, setRefreshing] = useState(false);
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [showCalendar, setShowCalendar] = useState(false);
    const { colors } = useTheme() as unknown as AppTheme;
    const { t } = useTranslation();
    const PAGE_SIZE = 10;
    const [page, setPage] = useState(1);
    const [taskData, setTaskData] = useState<TaskListData>({
        ...mockTasks,
        records: mockTasks.records.slice(0, PAGE_SIZE),
        has_more: mockTasks.records.length > PAGE_SIZE
    });
    const [slideAnim] = useState(new Animated.Value(0));
    const [fadeAnim] = useState(new Animated.Value(0));

    useEffect(() => {
        navigation.setOptions({
            headerTitle: () => (
                <View style={{ minWidth: '100%', flexDirection: 'row' }}>
                    <DateHeader
                        showIcon={true}
                        date={selectedDate}
                        onPress={() => setShowCalendar(!showCalendar)}
                        textStyle={{ color: colors.text }}
                        iconColor={colors.primary}
                        isCalendarVisible={showCalendar}
                    />
                </View>

            ),
        });
    }, [navigation, selectedDate, showCalendar, colors, setShowCalendar]);

    useEffect(() => {
        if (showCalendar) {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: true,
                }),
                Animated.timing(slideAnim, {
                    toValue: 0,
                    duration: 200,
                    useNativeDriver: true,
                })
            ]).start();
        } else {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 200,
                    useNativeDriver: true,
                }),
                Animated.timing(slideAnim, {
                    toValue: -ZINDEX.calendar,
                    duration: 200,
                    useNativeDriver: true,
                })
            ]).start();
        }
    }, [showCalendar]);

    const onRefresh = () => {
        setRefreshing(true);
        setPage(1);

        setTimeout(() => {
            setTaskData({
                ...mockTasks,
                records: mockTasks.records.slice(0, PAGE_SIZE),
                has_more: mockTasks.records.length > PAGE_SIZE
            });
            setRefreshing(false);
        }, 1000);
    };

    const handleDateSelect = (date: Date) => {
        setSelectedDate(date);
        // Here you could filter tasks based on the selected date
        // This would typically involve an API call in a real app
    };

    const handleLoadMore = () => {
        try {
            console.log('LoadMore triggered');
            console.log('Current page:', page);
            console.log('Current records length:', taskData.records.length);
            console.log('Has more:', taskData.has_more);

            if (!taskData.has_more) {
                console.log('No more data to load');
                return;
            }

            const nextPage = page + 1;
            const startIndex = (nextPage - 1) * PAGE_SIZE;
            const endIndex = startIndex + PAGE_SIZE;

            console.log('Loading items from', startIndex, 'to', endIndex);
            const newItems = mockTasks.records.slice(startIndex, endIndex);
            console.log('New items length:', newItems.length);

            if (newItems.length > 0) {
                setTaskData(prev => ({
                    ...prev,
                    records: [...prev.records, ...newItems],
                    has_more: endIndex < mockTasks.records.length
                }));
                setPage(nextPage);
            }
        } catch (error) {
            console.error('Error loading more tasks:', error);
        }
    };

    const handlePressOutside = () => {
        if (showCalendar) {
            setShowCalendar(false);
        }
    };


    return (
        <View style={styles.container}>

            <LinearGradient
                colors={colors.linearGradient}
                locations={GRADIENT_VALUE}
                style={styles.container}
            >
                {/* Conditionally render Calendar popup */}
                {showCalendar && (
                    <>
                        <Animated.View
                            style={[
                                styles.overlay,
                                {
                                    opacity: fadeAnim,
                                    pointerEvents: showCalendar ? 'auto' : 'none',
                                }
                            ]}
                        >
                            <TouchableWithoutFeedback onPress={handlePressOutside}>
                                <View style={styles.overlayTouchable} />
                            </TouchableWithoutFeedback>
                        </Animated.View>

                        <Animated.View
                            style={[
                                styles.calendarPopup,
                                {
                                    transform: [{ translateY: slideAnim }],
                                }
                            ]}
                        >
                            <TouchableWithoutFeedback>
                                <View>
                                    <Calendar2
                                        selectedDate={selectedDate}
                                        onDateSelect={(date: Date) => {
                                            handleDateSelect(date);
                                            setShowCalendar(false);
                                        }}
                                        onClose={() => setShowCalendar(false)}
                                    />
                                </View>
                            </TouchableWithoutFeedback>
                        </Animated.View>
                    </>
                )}

                {/* Header Section with WeekDays */}
                <View style={styles.headerSection}>
                    <WeekDays
                        selectedDate={selectedDate}
                        onDayPress={handleDateSelect}
                    />
                    <View style={styles.jobsContainer}>
                        <Text style={styles.jobsText}>{t('dashboard.tasks', { count: taskData.records.length })}</Text>
                        <CIcon source={Imap} size={(getResponsiveFontSize(30))} tintColor={COLORS.light.primary} />
                    </View>
                </View>

                {/* Body Section */}
                <TaskList
                    data={taskData}
                    onRefresh={onRefresh}
                    refreshing={refreshing}
                    onLoadMore={handleLoadMore}
                />
            </LinearGradient>
        </View>
    );
}

export default withResponsiveLayout(DashboardScreen);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.light.background,
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: ZINDEX.overlay,
    },
    overlayTouchable: {
        flex: 1,
    },
    calendarPopup: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: ZINDEX.calendar,
        backgroundColor: COLORS.light.white,
        shadowColor: COLORS.light.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        borderBottomLeftRadius: 15,
        borderBottomRightRadius: 15,
    },
    headerSection: {
        borderBottomColor: COLORS.light.border,
        marginBottom: SPACING.xs,
    },
    jobsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.xs,
        paddingTop: SPACING.size20,
    },
    jobsText: {
        fontSize: 16,
        fontWeight: '500',
    },
    iconImage: {
        width: 20,
        height: 20,
        tintColor: COLORS.light.primary,
    },
});