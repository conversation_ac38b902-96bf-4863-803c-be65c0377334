import React, { useState, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    Image,
    ActivityIndicator,
    TextInput,
    ImageProps,
    StyleSheet,
    ScrollView
} from 'react-native';
import { Controller } from 'react-hook-form';
import withForm from '../hocs/withForm';
import LinearGradient from 'react-native-linear-gradient';
import { NavigationProp, useNavigation, useTheme } from '@react-navigation/native';
import { Text } from 'react-native-gesture-handler';
import { forgotPasswordSchema } from '@/constants/validationSchema';
import { Ilogo, Iback } from '@/constants/UI/icons';
import { AppTheme, DrawerParamList } from '@/types';
import { GRADIENT_VALUE } from '@/constants/UI/themes';
import { useTranslation } from 'react-i18next';
import { createAuthStyles } from '@/styles/authStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as authUtils from '@/utils/authUtils';
import CIcon from '@/components/CIcon';
import { useToast } from '@/hocs/toast';
import authService from '@/services/authService';
import { useLoading } from '@/hocs/LoadingContext';
import { hi } from 'date-fns/locale';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import { removeExtraSpaces } from '@/utils/text';
import apiClient from '@/services/api/apiClient';

const STORAGE_KEYS = {
    REMEMBER_ME: 'remember_me',
    USER_EMAIL: 'user_email',
    USER_URL: 'user_url',
};

interface FormData {
    url: string;
    email: string;
}

const ForgotPasswordScreen = (props: any) => {
    const theme = useTheme() as unknown as AppTheme;
    const { colors } = theme;
    const { t } = useTranslation();
    const { handleSubmit, control, formState: { errors }, setValue } = props;
    const navigation = useNavigation<NavigationProp<DrawerParamList>>();
    const styles = createAuthStyles(theme);
    const [loading, setLoading] = useState(false);
    const toast = useToast();
    const { show, hide, withLoading } = useLoading();
    useEffect(() => {
        const loadSavedCredentials = async () => {
            try {
                const rememberedValue = await AsyncStorage.getItem(STORAGE_KEYS.REMEMBER_ME);
                
                const savedEmail = await AsyncStorage.getItem(STORAGE_KEYS.USER_EMAIL);
                const savedUrl = await AsyncStorage.getItem(STORAGE_KEYS.USER_URL);

                if (savedEmail) {
                    setValue('email', savedEmail);
                }

                if (savedUrl) {
                    setValue('url', savedUrl);
                }
            } catch (error) {
                console.error('Error loading saved credentials:', error);
            }
        };

        loadSavedCredentials();
    }, [setValue]);

    const onSubmit = async (data: FormData) => {
        try {
            show();
            await apiClient.setBaseUrl(data.url);

            const response = await authService.forgotPassword(data.email, data.url);

            if (response.success) {
                hide();
                toast.showToast(response.message || 'auth.passwordResetSent', 'success');
                setTimeout(() => {
                    navigation.navigate("Login");
                }, 3000);
            } else {
                hide();
                toast.showToast(response.message, 'error');
                // setError(response.message);
            }
        } catch (err: any) {
            setLoading(false);
            hide();
        }
    };

    const handleTextChange = (_field: string, text: string, onChange: (value: string) => void) => {

        switch (_field) {
            case 'url':
                onChange(`https://${removeExtraSpaces(text.trim().toLowerCase())}`);
                break;
            case 'email':
                onChange(removeExtraSpaces(text.trim().toLowerCase()));
                break;
            default:
                onChange(text);
        }
    };

    const handleBlur = (_field: string, value: string, onChange: (value: string) => void, onBlur?: () => void) => {

        switch (_field) {
            case 'url':
                if (!value || value === 'https://') {
                    onChange('');
                } else {
                    onChange(`https://${removeExtraSpaces(value.replace('https://', '').trim().toLowerCase())}`);
                }
                break;
            case 'email':
                onChange(removeExtraSpaces(value?.trim()?.toLowerCase() || ''));
                break;
        }
        if (onBlur) onBlur();
    };

    const renderForm = () => {
        const fields = ["url", "email"];
        return (
            <View style={styles.formContainer}>

                <View>
                    {fields.map((_field) => (
                        <View style={styles.fieldContainer} key={_field}>
                            <View style={styles.labelContainer}>
                                <Text style={styles.label}>
                                    {t(`fields.${_field}.label`)}
                                </Text>
                                <Text style={styles.required}>*</Text>
                            </View>
                            <Controller
                                control={control}
                                name={_field}
                                render={({ field: { onChange, value, onBlur } }) => (
                                    <View>
                                        {_field === 'url' && (
                                            <View style={styles.urlContainer}>
                                                <Text style={styles.urlPrefix}>https://</Text>
                                                <TextInput
                                                    placeholder={t('fields.url.placeholder')}
                                                    value={value?.replace('https://', '')?.replace(/\s+/g, '')?.toLowerCase()}
                                                    onChangeText={(text) => handleTextChange(_field, text, onChange)}
                                                    onBlur={() => handleBlur(_field, value, onChange, onBlur)}
                                                    style={styles.urlInput}
                                                    placeholderTextColor={colors.placeholder}
                                                />
                                            </View>
                                        )}
                                        {_field === 'email' && (
                                            <TextInput
                                                placeholder={t('fields.email.placeholder')}
                                                value={value?.replace(/\s+/g, '')?.toLowerCase()}
                                                onChangeText={(text) => handleTextChange(_field, text, onChange)}
                                                onBlur={() => handleBlur(_field, value, onChange, onBlur)}
                                                style={styles.input}
                                                placeholderTextColor={colors.placeholder}
                                                keyboardType="email-address"
                                                autoCapitalize="none"
                                            />
                                        )}
                                    </View>
                                )}
                            />
                            {errors[_field] && (
                                <Text style={styles.error}>{errors[_field]?.message as string}</Text>
                            )}
                        </View>
                    ))}

                </View>

                <TouchableOpacity
                    style={styles.actionButton}
                    onPress={!loading ? handleSubmit(onSubmit) : undefined}
                >
                    <Text style={styles.actionButtonText}>{t('auth.requestPasswordReset')}</Text>
                </TouchableOpacity>

            </View>
        );
    };

    return (
        <LinearGradient
            colors={colors.backgroundGradient}
            locations={GRADIENT_VALUE}
            style={styles.container}
        >
            <TouchableOpacity
                style={backButtonStyles.backButton}
                onPress={() => navigation.goBack()}
            >
                <CIcon source={Iback} size={24} tintColor={colors.text} />
            </TouchableOpacity>

            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
            >
                <View style={styles.logoContainer}>
                    <Image source={Ilogo as ImageProps} style={styles.logo} />
                </View>
                {renderForm()}
            </ScrollView>
        </LinearGradient>
    );
};

const backButtonStyles = StyleSheet.create({
    backButton: {
        position: 'absolute',
        width: 40,
        height: 40,
        top: 40,
        left: 16,
        zIndex: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

const EnhancedForgotPasswordScreen = withForm<FormData>(withResponsiveLayout(ForgotPasswordScreen));

export default () => (
    <EnhancedForgotPasswordScreen
        defaultValues={{
            url: 'https://',
            email: '',
        }}
        schema={forgotPasswordSchema}
        onSubmit={() => { }}
    />
);