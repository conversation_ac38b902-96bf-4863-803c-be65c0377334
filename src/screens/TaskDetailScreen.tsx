import React, { useEffect, useRef, useState } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    Image,
    StatusBar,
    Platform,
    FlatList,
    Alert,
} from 'react-native';
import { useNavigation, useRoute, useTheme, RouteProp } from '@react-navigation/native';
import { Task } from '@/interfaces/task';
import { SPACING, COLORS, BORDER_RADIUS, INSETS, FONT_WEIGHT, FONT_SIZE, ZINDEX } from '@/constants/UI/themes';
import { format } from 'date-fns';
import CIcon from '@/components/CIcon';
import {
    Iphone,
    Isetting,
    Icalender,
    Iperson,
    Imap,
    Iclose,
    Itrash,
    Ichat,
    Iarrow,
    ItoastError,
    Iback
} from '@/constants/UI/icons';
import { mockProductItems } from '@/constants/mock/products';
import { mockNotes } from '@/constants/mock/notes';
import ResponsiveScrollView from '@/components/ResponsiveScrollView';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import { defaultWidth, getResponsiveFontSize } from '@/constants/UI/responsive';
import NoteBottomSheet from '@/components/NoteBottomSheet';
import { useBottomSheet } from '../hocs/BottomSheetContext';
import { AppTheme, ThemeColors } from '@/types';
import ImagePicker from '@/components/ImagePicker';
import { PhotoDetail } from '@/types/image';
import CText from '@/components/CText';

type TaskDetailRouteProp = RouteProp<{ TaskDetail: { task: Task } }, 'TaskDetail'>;
const backButtonStyles = StyleSheet.create({
    backButton: {
        position: 'absolute',
        width: 40,
        height: 40,
        top: 40,
        left: 16,
        zIndex: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
const TaskDetailScreen = () => {
    const navigation = useNavigation();
    const route = useRoute<TaskDetailRouteProp>();
    const insets = useSafeAreaInsets();
    const { openBottomSheet, isBottomSheetOpen, closeBottomSheet } = useBottomSheet();

    const { task, photosFromCamera }: any = route?.params || {};
    const { colors } = useTheme() as unknown as AppTheme;
    const [activeTab, setActiveTab] = useState<'internal' | 'external'>('internal');
    const [photosTaked, setPhotosTaked] = useState<any[]>(photosFromCamera || []);
    const [photoDetails, setPhotoDetails] = useState<{ [key: string]: PhotoDetail }>({});

    const horizontalRef = useRef<FlatList>(null);


    useEffect(() => {
        navigation.setOptions({
            headerLeft: () => (
                <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    style={{ marginLeft: 16, padding: 8 }}
                    activeOpacity={0.7}
                >
                    <CIcon source={Iback} size={24} tintColor={colors.text} />
                </TouchableOpacity>
            ),
            title: 'Task Detail',
        });
    }, [navigation, colors]);

    useEffect(() => {
        if (photosFromCamera?.length > 0) {
            setPhotosTaked(photosFromCamera);
            // setPhotosTaked((prevPhotos: any[]) => {
            //     if (prevPhotos?.length > 0) {
            //         return [...photosFromCamera, ...prevPhotos];
            //     } else {
            //         return photosFromCamera
            //     }
            // });
        } else {
            // setPhotosTaked(photosFromCamera);
        }


    }, [photosFromCamera]);

    const formatDateTime = (dateString: string) => {
        const date = new Date(dateString);
        return `${format(date, 'MM/dd/yyyy')} • ${format(date, 'hh:mm a')}`;
    };

    const formatPhone = (phone: string) => {
        if (phone.startsWith('+')) {
            return phone;
        }
        return `+${phone}`;
    };

    const handleAddNote = (content: string) => {
        console.log('Add note:', content);
    };

    const handleEditNote = (id: string, content: string) => {
        console.log('Edit note:', id, content);
    };

    const handleDeleteNote = (id: string) => {
        console.log('Delete note:', id);
    };

    const handleOpenNotes = (type: 'internal' | 'external') => {
        // Convert mockNotes to array of note objects for demo
        const notes = [
            {
                id: '1',
                userId: '1', // fallback mock userId
                content: mockNotes[type][0].content,
                createdAt: new Date().toISOString(),
            },
        ];
        openBottomSheet({
            content: (
                <NoteBottomSheet
                    title={type === 'internal' ? 'Internal Notes' : 'External Notes'}
                    notes={mockNotes[type]}
                    onAdd={handleAddNote}
                    onEdit={handleEditNote}
                    onDelete={handleDeleteNote}
                    currentUserId={'1'}
                    onClose={closeBottomSheet}
                />
            ),
            snapPoints: ['60%', '90%'],
        });
    };

    const nextImage = () => {
        if (!photosTaked || photosTaked?.length <= 1) return;
        const index = photosTaked.findIndex(uri => uri === photosTaked[0]);
        if (index === -1) return;
        const nextIndex = (index + 1) % photosTaked?.length;
        setPhotosTaked(photosTaked[nextIndex]);
    };

    const prevImage = () => {
        if (!photosTaked || photosTaked?.length <= 1) return;
        const index = photosTaked.findIndex(uri => uri === photosTaked[0]);
        if (index === -1) return;
        const prevIndex = (index - 1 + photosTaked?.length) % photosTaked?.length;
        setPhotosTaked(photosTaked[prevIndex]);
    };

    const deletePhoto = (photoToDelete: any) => {
        Alert.alert(
            'Confirm Deletion',
            'Are you sure you want to delete this photo?',
            [
                {
                    text: 'Cancel',
                    style: 'cancel'
                },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => {
                        try {
                            if (photosTaked?.length === 1) {
                                setPhotosTaked([]);
                            }
                            setPhotosTaked((prevPhotos: any[] | null) => {
                                if (!prevPhotos) return [];
                                return prevPhotos.filter(photo => photo.uri !== photoToDelete.uri);
                            });
                        } catch (error) {
                            console.error('Error deleting photo:', error);
                            Alert.alert(
                                'Error',
                                'Could not delete the photo. Please try again.'
                            );
                        }
                    }
                }
            ]
        );
    };

    const renderItem = ({ item }: { item: any }) => (
        <TouchableOpacity
            style={styles.imageContainer}
        // onPress={() => setPhotosTaked(item)}
        >
            <TouchableOpacity
                onPress={() => deletePhoto(item)}
                style={styles.deleteImageButton}
            >
                <CIcon source={ItoastError} size={30} tintColor={colors.error} />
            </TouchableOpacity>
            <Image source={{ uri: item.uri }} style={styles.thumbnail} />
            <View style={styles.imageInfoOverlay}>
                <CText style={styles.imageInfoText}>{item.filename?.substring(0, 8)}...</CText>
            </View>
        </TouchableOpacity>
    );

    return (
        <>
            <ResponsiveScrollView
                style={styles.scrollView}
                contentContainerStyle={styles.scrollViewContent}
                withPadding={false}
            >
                {/* Task Info */}
                <View style={styles.taskInfoContainer}>
                    <View style={styles.taskCodeRow}>
                        <CText style={styles.taskCode}>#{task.task_code}</CText>
                        <TouchableOpacity>
                            <CIcon source={Isetting} size={20} tintColor="#000" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.dateTimeRow}>
                        <CIcon source={Icalender} size={20} tintColor="#000" style={styles.icon} />
                        <CText style={styles.dateTime}>
                            {formatDateTime(task.scheduled_from)}
                        </CText>
                    </View>

                    <View style={styles.separator} />

                    {/* Customer Info */}
                    <View style={styles.customerInfoRow}>
                        <CIcon source={Iperson} size={20} tintColor="#000" style={styles.icon} />
                        <CText style={styles.customerName}>
                            {task.customer.name}
                        </CText>
                    </View>

                    <View style={styles.customerInfoRow}>
                        <CIcon source={Imap} size={20} tintColor={COLORS.light.primary} style={styles.icon} />
                        <CText style={styles.customerAddress}>
                            {task.customer.address}
                        </CText>
                    </View>

                    <View style={styles.customerInfoRow}>
                        <CIcon source={Iphone} size={20} tintColor="#000" style={styles.icon} />
                        <CText style={styles.customerPhone}>
                            {formatPhone(task.customer.phone)}
                        </CText>
                    </View>
                </View>

                {/* Product Information */}
                <View style={styles.sectionContainer}>
                    <CText style={styles.sectionTitle}>Product Information</CText>

                    <View style={styles.productsContainer}>
                        {mockProductItems.map((item, index) => (
                            <View key={item.id} style={styles.productItem}>
                                {!item.type && (
                                    <View style={styles.productRow}>

                                        <View style={styles.productInfoContainer} >

                                            <View style={styles.productImageContainer}>
                                                {item.image ? (
                                                    <Image
                                                        source={item.image}
                                                        style={styles.productImage}
                                                    />
                                                ) : (
                                                    <View style={styles.productImagePlaceholder}>
                                                        <CText style={styles.productImagePlaceholderText}>
                                                            {item.name.substring(0, 2).toUpperCase()}
                                                        </CText>
                                                    </View>
                                                )}
                                            </View>

                                            <View style={styles.productInfo}>
                                                <View style={styles.productHeader}>
                                                    <CText style={styles.productName}>
                                                        {item.name}
                                                    </CText>
                                                </View>
                                                <CText style={styles.productModel}>
                                                    Model: {item.model}
                                                </CText>
                                                <CText style={styles.productQuantity}>
                                                    Quantity: {item.quantity} {item.unit}
                                                </CText>
                                            </View>

                                        </View>

                                        <View style={styles.productPriceContainer} >
                                            <TouchableOpacity style={styles.deleteButton}>
                                                <CText style={[styles.productPrice, { color: (colors as ThemeColors).price }]}>
                                                    ${item.price.toFixed(2)}
                                                </CText>
                                            </TouchableOpacity>
                                            <TouchableOpacity style={styles.deleteButton}>
                                                <CIcon source={Itrash} size={20} tintColor={COLORS.light.error} />
                                            </TouchableOpacity>

                                        </View>

                                    </View>
                                )}

                                {/* {item.type === 'service' && (
                                    <View style={styles.serviceRow}>
                                        <View style={styles.serviceInfo}>
                                            <View style={styles.productHeader}>
                                                <CText style={styles.productName}>
                                                    Service: {item.name}
                                                </CText>
                                                <CText style={styles.productPrice}>
                                                    ${item.price.toFixed(2)}
                                                </CText>
                                            </View>
                                            <CText style={styles.productName}>
                                                Product: {item.productName}
                                            </CText>
                                            <CText style={styles.productModel}>
                                                Model: {item.model}
                                            </CText>
                                        </View>
                                        <TouchableOpacity style={styles.deleteButton}>
                                            <CIcon source={Iclose} size={20} tintColor={COLORS.light.primary} />
                                        </TouchableOpacity>
                                    </View>
                                )} */}

                                {index !== mockProductItems.length - 1 && (
                                    <View style={styles.productSeparator} />
                                )}
                            </View>
                        ))}
                    </View>
                </View>

                {/* Notes Section */}
                <View style={styles.sectionContainer}>
                    <CText style={styles.sectionTitle}>Add notes</CText>

                    <View style={styles.tabContainer}>
                        <TouchableOpacity style={[styles.tab]} onPress={() => handleOpenNotes('internal')}>
                            <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '95%' }}>
                                <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '95%' }}>
                                    <CText style={[styles.tabText]}>Internal Notes</CText>
                                    <CIcon source={Ichat} size={24} tintColor={COLORS.light.primary} />
                                </View>
                                <CIcon source={Iarrow} size={30} tintColor={COLORS.light.primary} style={{ transform: [{ rotate: '90deg' }] }} />
                            </View>
                        </TouchableOpacity>
                        <View style={styles.separator} />
                        <TouchableOpacity style={[styles.tab, { flexDirection: 'column', alignItems: 'flex-start' }]} onPress={() => handleOpenNotes('external')}>
                            <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '95%' }}>
                                <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '95%' }}>
                                    <CText style={[styles.tabText]}>External Notes</CText>
                                </View>
                                <CIcon source={Iarrow} size={30} tintColor={COLORS.light.primary} style={{ transform: [{ rotate: '90deg' }] }} />
                            </View>
                            <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '95%' }}>
                                <CText style={[styles.subTabText]}>{mockNotes.internal.length}</CText>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={styles.sectionContainer}>
                    <CText style={styles.sectionTitle}>Images</CText>
                    <View style={[styles.tabContainer, { backgroundColor: 'transparent' }]}>
                        <ImagePicker onImagesSelected={(images) => { setPhotosTaked(images) }}
                            initialImages={photosTaked}
                        />
                    </View>
                </View>

                {photosTaked?.length > 0 && <View style={[styles.sectionContainer]}>
                    <CText style={styles.sectionTitle}>Attached images</CText>
                    <View style={[styles.tabContainer, { backgroundColor: 'transparent' }]}>
                        <FlatList
                            key={"horizontal-1"}
                            ref={horizontalRef}
                            data={photosTaked}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => `photo-${index + item.uri}`}
                            numColumns={3}
                            contentContainerStyle={styles.photoList}
                            style={{ borderWidth: 2, borderColor: colors.border, borderRadius: 8, padding: 8, maxHeight: 200 }}
                            showsVerticalScrollIndicator={true}
                            showsHorizontalScrollIndicator={true}
                        />
                    </View>
                </View>}

            </ResponsiveScrollView>
            <View style={[
                styles.bottomContainer,
                {
                    paddingBottom: Platform.OS === 'android' ?
                        Math.max(insets.bottom, INSETS.navigation.android) + INSETS.button.android :
                        insets.bottom || SPACING.md
                }
            ]}>
                <TouchableOpacity style={styles.startButton}>
                    <CText style={styles.startButtonText}>Start Job</CText>
                </TouchableOpacity>
            </View>

        </>



    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5faff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        backgroundColor: COLORS.light.white,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    backButton: {
        paddingRight: SPACING.md,
    },
    headerTitle: {

        fontSize: getResponsiveFontSize(FONT_SIZE.size26),
        fontWeight: FONT_WEIGHT.medium,
    },
    callButton: {
        padding: SPACING.xs,
    },
    scrollView: {
        flex: 1,
    },
    scrollViewContent: {
        flexGrow: 1,
    },
    taskInfoContainer: {
        backgroundColor: COLORS.light.white,
        padding: SPACING.md,
        marginBottom: SPACING.xs,
    },
    taskCodeRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: SPACING.md,
    },
    taskCode: {
        fontSize: getResponsiveFontSize(SPACING.size20),
        fontWeight: FONT_WEIGHT.medium,
    },
    dateTimeRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.md,
    },
    dateTime: {
        fontSize: 15,
    },
    separator: {
        height: 1,
        backgroundColor: COLORS.light.primary,
        marginVertical: SPACING.md,
        width: '100%',
    },
    customerInfoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.sm,
    },
    icon: {
        marginRight: SPACING.sm,
        width: 20,
    },
    customerName: {
        fontSize: getResponsiveFontSize(SPACING.size18),
        fontWeight: FONT_WEIGHT.medium,
    },
    customerAddress: {
        fontSize: getResponsiveFontSize(SPACING.size18),
        color: '#444',
        flex: 1,
    },
    customerPhone: {
        fontSize: getResponsiveFontSize(SPACING.size18),
    },
    sectionContainer: {
        marginBottom: SPACING.md,
    },
    sectionTitle: {
        fontSize: getResponsiveFontSize(SPACING.size20),
        fontWeight: FONT_WEIGHT.medium,
        marginBottom: SPACING.md,
        paddingHorizontal: SPACING.md,
    },
    productsContainer: {
        backgroundColor: COLORS.light.white,
        paddingHorizontal: SPACING.md,
    },
    productItem: {
        paddingVertical: SPACING.sm,
    },
    productRow: {
        flexDirection: 'row',
        width: '100%',
    },
    serviceRow: {
        flexDirection: 'row',
        padding: SPACING.sm,
        justifyContent: 'space-between',
    },
    productImageContainer: {
        width: 60,
        height: 60,
        borderRadius: BORDER_RADIUS.sm,
        overflow: 'hidden',
        marginRight: SPACING.sm,
    },
    productImage: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    productImagePlaceholder: {
        width: '100%',
        height: '100%',
        backgroundColor: '#f0f0f0',
        borderRadius: BORDER_RADIUS.sm,
        justifyContent: 'center',
        alignItems: 'center',
    },
    productImagePlaceholderText: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        fontWeight: FONT_WEIGHT.medium,
        color: '#888',
    },
    productInfo: {
        width: '100%',
    },
    serviceInfo: {
        flex: 1,
    },
    productHeader: {
        maxWidth: '80%',
    },
    productName: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        fontWeight: FONT_WEIGHT.medium,
    },
    productPrice: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        color: COLORS.light.primary,
        fontWeight: FONT_WEIGHT.medium,
    },
    productModel: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        color: '#666',
        marginVertical: 2,
    },
    productQuantity: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        color: '#666',
    },
    deleteButton: {
        padding: SPACING.xs,
        alignSelf: 'flex-end',
    },
    deleteImageButton: {
        top: 0,
        right: 0,
        position: 'absolute',
        zIndex: ZINDEX.elevated,
    },
    productSeparator: {
        height: 1,
        backgroundColor: COLORS.light.primary,
        marginTop: SPACING.sm,
        width: '100%',
    },
    tabContainer: {
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: SPACING.md,
        paddingHorizontal: SPACING.md,
        backgroundColor: COLORS.light.white,
    },
    tab: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        flex: 1,
        paddingVertical: SPACING.sm,
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
        width: '100%',
    },
    tabText: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        fontWeight: FONT_WEIGHT.medium,
        marginRight: SPACING.sm,
    },
    subTabText: {
        color: '#888',
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
    },
    notesContent: {
        paddingVertical: SPACING.sm,
    },
    notesText: {
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        lineHeight: 22,
        color: '#444',
    },
    bottomContainer: {
        padding: SPACING.md,
    },
    startButton: {
        backgroundColor: COLORS.light.primary,
        borderRadius: BORDER_RADIUS.md,
        padding: SPACING.md,
        alignItems: 'center',
    },
    startButtonText: {
        color: '#fff',
        fontSize: getResponsiveFontSize(FONT_SIZE.size18),
        fontWeight: FONT_WEIGHT.medium,
    },
    productPriceContainer: {
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        minWidth: '20%',
    },
    productInfoContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        maxWidth: '80%',
    },
    imageContainer: {
        margin: 4,
        borderRadius: 8,
        overflow: 'hidden',
        position: 'relative',
    },
    thumbnail: {
        width: defaultWidth * 0.28,
        height: defaultWidth * 0.28,
        borderRadius: 10,
    },
    imageInfoOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
        padding: 4,
        flexDirection: 'row',
        alignItems: 'center',
    },
    imageInfoText: {
        color: 'white',
        fontSize: 12,
        marginLeft: 4,
    },
    photoList: {
        paddingTop: 8,
    },
});

const TaskDetailWithLayout = withResponsiveLayout(TaskDetailScreen);
export default function TaskDetailContainer() {
    return <TaskDetailWithLayout linearGradient={true} />;
}