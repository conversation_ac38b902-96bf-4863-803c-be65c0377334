import React from 'react';
import { View, StyleSheet } from 'react-native';
import NotificationList from '@/components/NotificationList';
import { mockNotifications } from '@/constants/mock/notifications';
import { useTheme } from '@react-navigation/native';
import { AppTheme } from '@/types';
const { colors } = useTheme() as unknown as AppTheme;

export default function NotificationScreen() {
    return (
        <View style={styles.container}>
            <NotificationList notifications={mockNotifications} />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.green,
    },
});