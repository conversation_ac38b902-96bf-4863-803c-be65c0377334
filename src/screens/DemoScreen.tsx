import {
    TextStyle,
    Image,
    TouchableOpacity,
    Modal,
    FlatList,
    ImageBackground,
    Dimensions,
    Alert,
    Platform,
} from 'react-native';
import React, { useState, useEffect, useRef } from 'react';
import {
    Text,
    StyleSheet,
    View,
} from 'react-native';
import { Icamera, Iscan, ItoastError } from '@/constants/UI/icons';
import CText from '@/components/CText';
import { useTheme } from '@react-navigation/native';
import { SPACING, PADDING, FONT_WEIGHT, ZINDEX, COLORS } from '@/constants/UI/themes';
import { useToast } from '@/hocs/toast';
import withResponsiveLayout from '@/hocs/withResponsiveLayout';
import { useTranslation } from 'react-i18next';
import CameraBase from '@/components/CameraBase';
import CIcon from '@/components/CIcon';
import { format } from 'date-fns';
import { getCommonStyles } from '@/constants/UI/responsiveStyles';
import Barcode from '@kichiyaki/react-native-barcode-generator';
import { AppTheme } from '@/types/theme';
import { defaultWidth } from '@/constants/UI/responsive';
import { usePermission } from '@/hooks/usePermission';
import { openSettings } from 'react-native-permissions';
import GradllyModel from '@/components/ImagePicker/GalleryModel';
import { PhotoDetail } from '@/types/image';


function DemoScreen(props: any) {
    const { colors } = useTheme() as unknown as AppTheme;
    const { showToast } = useToast();
    const { t } = useTranslation();
    const commonsStyle = getCommonStyles();
    const [photosTaked, setPhotosTaked] = useState<any[]>([]);
    const [photoDetails, setPhotoDetails] = useState<{ [key: string]: PhotoDetail }>({});
    const [selectedPhoto, setSelectedPhoto] = useState<any | null>(null);
    const [cameraVisible, setCameraVisible] = useState(false);
    const [scannedCode, setScannedCode] = useState<string | null>(null);
    const [cameraMode, setCameraMode] = useState('scan');
    const [showPhotoTaken, setShowPhotoTaken] = useState(false);

    const horizontalRef = useRef<FlatList>(null);

    const {
        requestMediaPermissions,
        requestBothStoragePermissions,
        request: requestPhotoLibraryPermission
    } = usePermission('photoLibrary');

    useEffect(() => {
        if (photosTaked && photosTaked.length > 0) {
            const details: any = {};
            photosTaked.forEach(image => {
                details[image.uri] = {
                    date: new Date(),
                    name: image.uri.split('/').pop() || 'Unknown',
                    size: Math.floor(Math.random() * 5 + 1) + 'MB'
                };
            });
            setPhotoDetails(details);
        }
    }, [photosTaked]);

    const toggleCameraVisible = () => {
        setCameraVisible(!cameraVisible);
    };

    const handleCodeScanned = (code: string) => {
        setScannedCode(code);
        setCameraVisible(false);
        showToast(`Barcode scanned: ${code}`, 'success');
    };

    const checkPhotoLibraryPermission = async () => {
        try {
            let granted = false;
            if (Platform.OS === 'ios') {
                granted = await requestPhotoLibraryPermission();
            } else {
                if (Number(Platform.Version) >= 33) {
                    granted = await requestMediaPermissions?.() || false;
                } else {
                    granted = await requestBothStoragePermissions?.() || false;
                }
            }

            if (!granted) {
                Alert.alert(
                    'Permission Required',
                    'The app needs photo library access to select images',
                    [{ text: 'OK', onPress: () => openSettings() }],
                );
                setCameraVisible(false);
                return;
            } else {
                setShowPhotoTaken(true);
            }
        } catch (error) {
            console.error('Error requesting photo library permission:', error);
            return false;
        }
    };

    const nextImage = () => {
        if (!photosTaked || photosTaked.length <= 1) return;
        const index = photosTaked.findIndex(uri => uri === selectedPhoto);
        if (index === -1) return;
        const nextIndex = (index + 1) % photosTaked.length;
        setSelectedPhoto(photosTaked[nextIndex]);
    };

    const prevImage = () => {
        if (!photosTaked || photosTaked.length <= 1) return;
        const index = photosTaked.findIndex(uri => uri === selectedPhoto);
        if (index === -1) return;
        const prevIndex = (index - 1 + photosTaked.length) % photosTaked.length;
        setSelectedPhoto(photosTaked[prevIndex]);
    };

    const deletePhoto = (photoToDelete: any) => {
        Alert.alert(
            'Confirm Deletion',
            'Are you sure you want to delete this photo?',
            [
                {
                    text: 'Cancel',
                    style: 'cancel'
                },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => {
                        try {
                            if (selectedPhoto && selectedPhoto.uri === photoToDelete.uri) {
                                setSelectedPhoto(null);
                            }
                            setPhotosTaked((prevPhotos: any[] | null) => {
                                if (!prevPhotos) return [];
                                return prevPhotos.filter(photo => photo.uri !== photoToDelete.uri);
                            });
                        } catch (error) {
                            console.error('Error deleting photo:', error);
                            Alert.alert(
                                'Error',
                                'Could not delete the photo. Please try again.'
                            );
                        }
                    }
                }
            ]
        );
    };

    const renderItem = ({ item }: { item: any }) => (
        <TouchableOpacity
            style={styles.imageContainer}
            onPress={() => setSelectedPhoto(item)}
        >
            <TouchableOpacity
                onPress={() => deletePhoto(item)}
                style={styles.deleteButton}
            >
                <CIcon source={ItoastError} size={30} tintColor={colors.error} />
            </TouchableOpacity>
            <Image source={{ uri: item.uri }} style={styles.thumbnail} />
            <View style={styles.imageInfoOverlay}>
                <CText style={styles.imageInfoText}>{item.filename?.substring(0, 8)}...</CText>
            </View>
        </TouchableOpacity>
    );

    return (
        <>
            {/* Camera mode toggle buttons */}
            <View style={styles.cameraModeContainer}>
                <TouchableOpacity
                    style={[styles.modeButton,
                    cameraMode === 'scan' && styles.activeMode
                    ]}
                    onPress={() => setCameraMode('scan')}
                >
                    <CIcon source={Iscan} size={24} tintColor={'#ffffff'} />
                    <Text style={[styles.modeText, { color: '#ffffff' }]}>Scan Code</Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[styles.modeButton,
                    cameraMode === 'photo' && styles.activeMode
                    ]}
                    onPress={() => setCameraMode('photo')}
                >
                    <CIcon source={Icamera} size={24} tintColor={'#ffffff'} />
                    <Text style={[styles.modeText, { color: '#ffffff' }]}>Take Photo</Text>
                </TouchableOpacity>
            </View>

            <View style={{
                flex: 1,
                padding: 4,
                justifyContent: 'center',
                paddingBottom: '35%'
            }}>
                {/* <Text> {defaultWidth} {defaultHeight}</Text> */}
                {cameraMode === 'scan' && scannedCode
                    ? <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                        <Text>{scannedCode}</Text>
                        <View style={{ margin: 10 }} />
                        <Barcode
                            format="CODE128"
                            value={scannedCode}
                            text={scannedCode}
                            style={{ marginBottom: 40 }}
                            maxWidth={Dimensions.get('window').width / 2}
                        />
                    </View>
                    : cameraMode == 'photo' && photosTaked && photosTaked.length > 0
                        ? (
                            <View >
                                <FlatList
                                    key={"horizontal-1"}
                                    ref={horizontalRef}
                                    data={photosTaked}
                                    renderItem={renderItem}
                                    keyExtractor={(item, index) => `photo-${index + item.uri}`}
                                    numColumns={3}
                                    contentContainerStyle={styles.photoList}
                                    style={{ borderWidth: 2, borderColor: colors.border, borderRadius: 8, padding: 8, maxHeight: 200 }}
                                    showsVerticalScrollIndicator={true}
                                    showsHorizontalScrollIndicator={true}

                                />
                                <View style={{ height: 50 }} />
                                {/* <FlatList
                                    key={"horizontal-2"}
                                    ref={verticalRef}
                                    data={photosTaked}
                                    renderItem={renderItem}
                                    keyExtractor={(item, index) => `photo-${index + item.uri}`}
                                    contentContainerStyle={styles.photoList}
                                    horizontal={true}
                                    showsHorizontalScrollIndicator={true}
                                    showsVerticalScrollIndicator={true}
                                    style={{ borderWidth: 2, borderColor: colors.border, borderRadius: 8, padding: 8 }}
                                /> */}
                            </View>

                        ) : (
                            <></>
                        )}


                <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', flexDirection: 'row' }}>

                    {cameraMode === 'photo' && <TouchableOpacity style={commonsStyle.button} onPress={checkPhotoLibraryPermission}>
                        <Text style={commonsStyle.buttonText}>Attach Image</Text>
                    </TouchableOpacity>}
                    <View style={{ width: 10 }} />
                    <TouchableOpacity style={commonsStyle.button} onPress={toggleCameraVisible}>
                        <Text style={commonsStyle.buttonText}>Open Camera</Text>
                    </TouchableOpacity>
                </View>

            </View >

            {/* Photo detail modal */}
            <Modal
                visible={!!selectedPhoto}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setSelectedPhoto(null)}
            >
                <View style={styles.modalContainer}>
                    {/* Close button */}
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={() => setSelectedPhoto(null)}
                    >
                        <CText style={styles.closeButtonText}>✕</CText>
                    </TouchableOpacity>

                    {/* Previous image button */}
                    {photosTaked && photosTaked.length > 1 && (
                        <TouchableOpacity style={styles.prevButton} onPress={prevImage}>
                            <CText style={styles.navButtonText}>◀</CText>
                        </TouchableOpacity>
                    )}

                    {/* Display image */}
                    {selectedPhoto && (
                        <ImageBackground
                            source={{ uri: selectedPhoto.uri }}
                            style={styles.fullImage}
                            resizeMode="contain"
                        >
                            <TouchableOpacity
                                style={StyleSheet.absoluteFill}
                                activeOpacity={1}
                                onPress={() => setSelectedPhoto(null)}
                            />
                        </ImageBackground>
                    )}

                    {/* Next image button */}
                    {photosTaked && photosTaked.length > 1 && (
                        <TouchableOpacity style={styles.nextButton} onPress={nextImage}>
                            <CText style={styles.navButtonText}>▶</CText>
                        </TouchableOpacity>
                    )}

                    {/* Image details */}
                    {selectedPhoto && (
                        <View style={styles.photoDetailsContainer}>
                            <CText style={styles.photoDetailsTitle}>{selectedPhoto?.filename || 'Unknown'}</CText>
                            <View style={styles.photoDetailsRow}>
                                <View style={styles.detailItem}>
                                    <CText style={styles.detailText}>
                                        {format(photoDetails[selectedPhoto]?.date || new Date(), 'dd/MM/yyyy HH:mm')}
                                    </CText>
                                </View>
                            </View>

                        </View>
                    )}
                </View>
            </Modal>
            {
                cameraVisible && (
                    <View style={styles.cameraContainer}>
                        <CameraBase
                            onCodeScanned={handleCodeScanned}
                            onClose={() => setCameraVisible(false)}
                            // isScanMode={cameraMode === 'scan'}
                            initialCameraPosition="back"
                            initialFlashMode="auto"
                            onPhotoTaken={(image: any) => {
                                setPhotosTaked(image)
                            }}
                            photosTaked={photosTaked || []}
                        />

                    </View>
                )
            }

            <GradllyModel
                visible={showPhotoTaken}
                onClose={() => setShowPhotoTaken(false)}
                onSelect={(selectedImages: any) => {
                    setPhotosTaked(selectedImages as any)
                    setShowPhotoTaken(false)
                }}
                multiple={true}
                photosTaken={photosTaked}
            />

        </>
    );
}

const styles = StyleSheet.create({
    imageContainer: {
        margin: 4,
        borderRadius: 8,
        overflow: 'hidden',
        position: 'relative',
    },
    thumbnail: {
        width: defaultWidth * 0.28,
        height: defaultWidth * 0.28,
        borderRadius: 10,
    },
    imageInfoOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
        padding: 4,
        flexDirection: 'row',
        alignItems: 'center',
    },
    imageInfoText: {
        color: 'white',
        fontSize: 12,
        marginLeft: 4,
    },
    card: {
        flex: 1,
        minWidth: 300,
        marginBottom: SPACING.md,
    },
    section: {
        width: '100%',
        marginBottom: SPACING.lg,
    },
    input: {
        marginBottom: PADDING.md,
    },
    cardTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: SPACING.sm,
    } as TextStyle,
    cardText: {
        fontSize: 16,
        marginBottom: SPACING.md,
    } as TextStyle,
    cardActions: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: SPACING.sm,
    },
    customCard: {
        backgroundColor: '#f5f5f5',
    },
    demoSection: {
        marginBottom: SPACING.md,
    },
    demoSectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: SPACING.sm,
    },
    imagePreviewContainer: {
        marginTop: SPACING.md,
        alignItems: 'center',
    },
    singleImagePreview: {
        width: '100%',
        height: 200,
        borderRadius: 8,
    },
    multipleImagesScrollView: {
        marginTop: SPACING.md,
    },
    multipleImagePreview: {
        width: 150,
        height: 150,
        borderRadius: 8,
        marginRight: SPACING.sm,
    },
    submitButtonContainer: {
        marginTop: SPACING.md,
    },
    submitButton: {
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    submitButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    helperText: {
        fontSize: 14,
        marginTop: SPACING.sm,
    } as TextStyle,
    scanButton: {
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 10,
    },
    scanButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    scannedResultContainer: {
        marginTop: 20,
        alignItems: 'center',
    },
    scannedResultLabel: {
        fontSize: 14,
        marginBottom: 8,
        fontWeight: '500',
    },
    scannedCodeContainer: {
        backgroundColor: '#f5f5f5',
        padding: 12,
        borderRadius: 8,
        width: '100%',
    },
    scannedCode: {
        fontSize: 16,
        textAlign: 'center',
    },
    mapContainer: {
        height: 400,
        borderRadius: 8,
        overflow: 'hidden',
        marginTop: 16,
    },
    mapToggleButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 4,
        alignItems: 'center',
        alignSelf: 'flex-start',
    },
    toggleButtonText: {
        color: 'white',
        fontWeight: '500',
    },
    cameraContainer: {
        flex: 1,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
    },
    cameraModeContainer: {
        position: 'absolute',
        bottom: 80,
        left: 20,
        right: 20,
        flexDirection: 'row',
        justifyContent: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderRadius: 25,
        padding: 5,
        zIndex: 100
    },
    modeButton: {
        flex: 1,
        alignItems: 'center',
        padding: 10,
        borderRadius: 20,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    activeMode: {
        backgroundColor: '#2196F3',
    },
    modeText: {
        color: 'white',
        marginLeft: 5,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    emptyText: {
        marginTop: 8,
        color: COLORS.light.textSecondary,
        textAlign: 'center',
    },

    headerButton: {
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },

    headerActions: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    deleteButton: {
        marginRight: 16,
        position: 'absolute',
        top: -0,
        right: -15,
        zIndex: ZINDEX.elevated,
    },

    container: {
        flex: 1,
        paddingHorizontal: 8,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: FONT_WEIGHT.semiBold,
    },
    photoList: {
        paddingTop: 8,
    },


    modalContainer: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.9)',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        top: 40,
        right: 20,
        zIndex: 10,
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    fullImage: {
        width: '100%',
        height: '80%',
    },
    photoDetailsContainer: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.7)',
        padding: 16,
    },
    photoDetailsTitle: {
        color: 'white',
        fontSize: 18,
        fontWeight: FONT_WEIGHT.semiBold,
        marginBottom: 8,
    },
    photoDetailsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 4,
    },
    detailText: {
        color: 'white',
        fontSize: 14,
        marginLeft: 8,
    },
    prevButton: {
        position: 'absolute',
        left: 20,
        top: '50%',
        zIndex: 10,
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        transform: [{ translateY: -25 }],
    },
    nextButton: {
        position: 'absolute',
        right: 20,
        top: '50%',
        zIndex: 10,
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        transform: [{ translateY: -25 }],
    },
    navButtonText: {
        color: 'white',
        fontSize: 20,
        fontWeight: 'bold',
    },
    closeButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
    },
    modalButtonsContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 16,
    },
    modalButton: {
        backgroundColor: '#2196F3',
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 8,
        marginHorizontal: 8,
    },
    modalButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: FONT_WEIGHT.medium,
    },
});

export default withResponsiveLayout(DemoScreen);

