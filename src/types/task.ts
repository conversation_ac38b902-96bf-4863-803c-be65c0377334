export interface GetTaskResponse {
    status: string;
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
    records: TaskRecord[];
}

export interface Product {
    id: number;
    name: string;
    qty: number;
    description: string;
    under_warranty: boolean;
}

export interface Customer {
    name: string;
    phone: string;
    address: string;
}

interface RepairProduct {
    id: number;
    name: string;
    qty: number;
    description: string;
    under_warranty: boolean;
}

export interface Photo {
    // Define when structure is known
}

export interface Note {
    // Define when structure is known
}

export interface Currency {
    name: string;
    symbol: string;
}

// TaskRecord (base record)
export interface TaskRecord {
    task_id: number;
    task_code: string;
    task_type_icon_url: string;
    task_status: string;
    scheduled_from: string;
    scheduled_to: string;
    customer: Customer;
    repair_product: string; // This differs in detail record
}

// TaskDetailRecord extends TaskRecord
export interface TaskDetailRecord extends Omit<TaskRecord, 'repair_product'> {
    status: string;
    work_started_at: string;
    work_ended_at: string;
    repair_product: RepairProduct; // override
    task_lines: TaskRecord[];
    signature: string;
    photos: Photo[];
    internal_notes: Note[];
    external_notes: Note[];
    subtotal: number;
    tax_amount: number;
    total_amount: number;
    paid_amount: number;
    balance_amount: number;
    currency: Currency;
    issue_reason: string;
}
