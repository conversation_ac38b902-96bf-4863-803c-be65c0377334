import { User } from "./user";
export interface FormLogin {
    url: string;
    email: string;
    password: string;
}

export interface FormForgotPassword {
    email: string;
}

export interface LoginData {
    email: string;
    password: string;
    url: string;
}


// export interface LoginResponse {
//     access_token?: string;
//     refresh_token?: string;
// }
export interface LoginResponse {
    status: string;
    session_id: string;
    user: User;
}

export interface ApiResponse {
    success: boolean;
    message: string;
    data?: any | LoginResponse;
    user?: Partial<User>;
    detail?: string;
    code?: string;
    response?: any;
}

export interface TokenPayloadDecoded {
    session_id: string;
    url: string;
    user_id: number;
    iss: string;
    aud: string;
    exp: number;
}

