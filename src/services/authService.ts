import { LoginData } from '@/types';
import { ENDPOINTS } from '@/constants/api';
import * as authUtils from '@/utils/authUtils';
import i18n from '@/i18n/i18n';
import apiClient from '@/services/api/apiClient';
import { ApiResponse } from '@/types/auth';

//=======================================================
export const login = async (loginData: LoginData): Promise<ApiResponse> => {
    try {
        const response = await apiClient.post<ApiResponse>(ENDPOINTS.auth.login, {
            email: loginData.email,
            password: loginData.password,
            url: loginData.url
        });


        // if (!response.success) {
        //     if (response?.code === "ECONNABORTED" || response?.code === "ERR_NETWORK" || response?.code === "ERR_BAD_REQUEST" || response?.status === 422 || response?.status === 503) {

        //         if (response?.status === 503) {
        //             return {
        //                 ...response,
        //                 message: 'Service Unavailable' || response?.code
        //             };
        //         }

        //         if (response?.status === 422) {
        //             return {
        //                 ...response,
        //                 message: response?.response?.data?.detail[0]?.msg || response?.code
        //             };
        //         }

        //         return {
        //             ...response,
        //             message: i18n.t('errors.http.timeout') as string
        //         };
        //     }

        //     return {
        //         ...response,
        //         message: response?.response?.data?.detail || response?.message || i18n.t('errors.loginFailed') as string
        //     };
        // }

        const ERROR_MESSAGES = {
            503: 'Service Unavailable',
            422: response?.response?.data?.detail[0]?.msg,
            TIMEOUT: i18n.t('errors.http.timeout'),
            DEFAULT: response?.response?.data?.detail || response?.message || i18n.t('errors.loginFailed')
        };

        const isNetworkError = response?.code ? ['ECONNABORTED', 'ERR_NETWORK', 'ERR_BAD_REQUEST'].includes(response.code) : false;
        const isStatusError = response?.status ? [422, 503].includes(response.status) : false;

        if (isNetworkError || isStatusError) {
            return {
                ...response,
                message: ERROR_MESSAGES[response?.status as keyof typeof ERROR_MESSAGES] || ERROR_MESSAGES.TIMEOUT
            };
        }

        if (!response.success) {
            // Check for email/password errors
            if (
                response?.response?.data?.detail &&
                (
                    response?.response?.data?.detail.toLowerCase().includes('email') ||
                    response?.response?.data?.detail.toLowerCase().includes('password') ||
                    response?.response?.data?.detail.toLowerCase().includes('credentials')
                )
            ) {
                return {
                    ...response,
                    message: ERROR_MESSAGES[response?.status as keyof typeof ERROR_MESSAGES] || ERROR_MESSAGES.TIMEOUT
                };
            }

            // Check for URL errors
            if (
                response?.code === "ECONNABORTED" ||
                response?.code === "ERR_NETWORK" ||
                (response?.response?.data?.detail && response?.response?.data?.detail.toLowerCase().includes('url'))
            ) {
                return {
                    ...response,
                    message: i18n.t('errors.http.incorrectUrl') as string
                };
            }

            return {
                ...response,
                message: ERROR_MESSAGES.DEFAULT
            };
        }

        return {
            ...response, message: i18n.t('auth.loginSuccess'),
        };
    } catch (error: any) {
        console.error('Login error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};
//=======================================================
export const refreshToken = async (refreshToken: string): Promise<ApiResponse> => {
    try {
        // ApiClient will handle automatic token refresh
        // But we can manually call it if needed
        const response = await apiClient.post<any>(ENDPOINTS.auth.login, { refreshToken });

        if (!response.success) {
            return {
                success: false,
                message: response.message || i18n.t('errors.tokenRefreshFailed')
            };
        }

        const { token, refreshToken: newRefreshToken, user } = response.data || {};

        if (token) {
            await authUtils.saveToken(token, newRefreshToken);
        }

        return {
            ...response, message: i18n.t('auth.tokenRefreshed'),
        };
    } catch (error: any) {
        console.error('Refresh token error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};

//=======================================================
export const logout = async (): Promise<{ success: boolean; message: string; }> => {
    try {
        const response = await apiClient.post<any>(ENDPOINTS.auth.logout);
        await authUtils.clearSessionId();

        return {
            ...response as ApiResponse,
            success: response.success,
        };
    } catch (error: any) {
        console.error('Logout error:', error);
        await authUtils.clearSessionId();

        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};

//=======================================================
export const forgotPassword = async (email: string, url?: string): Promise<{ success: boolean; message: string; }> => {
    try {
        const response = await apiClient.post<any>(ENDPOINTS.auth.forgotPassword, { email, url });

        if (response.success) {
            return {
                success: response.success,
                message: response?.data.message || i18n.t('auth.passwordResetSent')
            }
        }
        else if (response?.code === "ECONNABORTED" || response?.code === "ERR_NETWORK" || response?.response?.data?.detail) {
            return {
                ...response,
                message: response?.response?.data?.detail || i18n.t('errors.http.timeoutForgotPasswod') as string || i18n.t('errors.somethingWentWrong')
            };
        }

        return {
            success: false,
            message: i18n.t('errors.somethingWentWrong')
        };

    } catch (error: any) {
        console.error('Forgot password error:', error);
        return {
            success: false,
            message: error.message || i18n.t('errors.somethingWentWrong')
        };
    }
};


export default {
    login,
    logout,
    refreshToken,
    forgotPassword,
};