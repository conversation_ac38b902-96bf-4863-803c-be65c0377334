import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, CancelTokenSource, InternalAxiosRequestConfig } from 'axios';
import { Platform, AppState, AppStateStatus } from 'react-native';
import * as authUtils from '@/utils/authUtils';
import { useToast } from '@/hocs/toast';
import i18n from '@/i18n/i18n';
import { API_TIMEOUT, DEFAULT_BASE_URL } from '@/constants/api';
import { HTTP_STATUS, HttpStatus } from '@/constants/httpStatus';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '@/constants/auth';
import { isIOS } from '@/constants/UI/responsive';

// Configuration for cURL logging
const CURL_LOGGING_CONFIG = {
    enabled: __DEV__, // Only enable in development
    includeHeaders: true,
    includeResponseTime: true,
    maxDataLength: 1000, // Limit data length in cURL for readability
};

// Types
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    status?: number;
    error?: any;
    code?: string;
    response?: any;
}

// Store pending requests when refreshing token
let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

// Active request tokens for cancellation
const pendingRequests: Map<string, CancelTokenSource> = new Map();

// Custom interface to extend InternalAxiosRequestConfig
interface CustomRequestConfig extends InternalAxiosRequestConfig {
    _retry?: boolean;
}

/**
 * ApiClient is a singleton class that provides methods for making API requests
 * with built-in error handling, token management, and request cancellation.
 */
class ApiClient {
    private static instance: ApiClient;
    private axiosInstance: AxiosInstance;
    private baseURL: string = DEFAULT_BASE_URL || 'incorrect-api-url';
    // private baseURL: string = 'incorrect-api-url';
    private appStateSubscription: any = null;

    private constructor() {
        this.initializeBaseUrl();
        this.axiosInstance = this.createAxiosInstance();
        this.setupInterceptors();
        this.monitorAppState();
    }

    /**
     * Initialize base URL from saved storage or default
     */
    private async initializeBaseUrl(): Promise<void> {
        try {
            const savedUrl = await AsyncStorage.getItem(STORAGE_KEYS.USER_URL);
            if (savedUrl) {
                this.baseURL = savedUrl;
                console.log('🔗 Loaded saved URL:', savedUrl);
            } else {
                console.log('🔗 Using default URL:', this.baseURL);
            }
        } catch (error) {
            console.error('Error loading saved URL:', error);
            console.log('🔗 Fallback to default URL:', this.baseURL);
        }
    }

    /**
     * Get the singleton instance of ApiClient
     */
    public static getInstance(): ApiClient {
        if (!ApiClient.instance) {
            ApiClient.instance = new ApiClient();
        }
        return ApiClient.instance;
    }

    /**
     * Create a new Axios instance with default configuration
     */
    private createAxiosInstance(): AxiosInstance {
        return axios.create({
            baseURL: this.baseURL,
            timeout: API_TIMEOUT,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Platform': Platform.OS,
                'X-Platform-Version': Platform.Version.toString(),
            },
        });
    }

    /**
     * Setup Axios request and response interceptors
     */
    private setupInterceptors(): void {
        // Request interceptor
        this.axiosInstance.interceptors.request.use(
            async (config: InternalAxiosRequestConfig) => {
                // Get current base URL
                config.baseURL = this.baseURL;

                // Add auth token if available
                try {
                    const sessionId = await authUtils.getSessionId();
                    if (sessionId) {
                        config.headers['session-id'] = sessionId;
                    }
                } catch (error) {
                    console.error('Error getting session id:', error);
                }

                // Set up request cancellation
                const requestId = `${config.method}-${config.url}-${Date.now()}`;
                const source = axios.CancelToken.source();
                config.cancelToken = source.token;
                pendingRequests.set(requestId, source);

                // Log request as curl command (for debugging)
                if (__DEV__) {
                    const curlCommand = this.requestToCurl(config);
                    // console.log('🌐 cURL:', curlCommand);
                    // Also log to native console for terminal visibility
                    console.warn('🌐 cURL:', curlCommand);
                }

                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // Response interceptor
        this.axiosInstance.interceptors.response.use(
            (response: AxiosResponse) => {
                // Log response information if cURL logging is enabled
                if (CURL_LOGGING_CONFIG.enabled && CURL_LOGGING_CONFIG.includeResponseTime) {
                    const requestStartTime = (response.config as any)._requestStartTime;
                    if (requestStartTime) {
                        const responseTime = Date.now() - requestStartTime;
                        console.log(`\n✅ API Response [${response.status}] - ${responseTime}ms`);
                        console.log(`📍 ${response.config.method?.toUpperCase()} ${response.config.url}`);
                        console.log('━'.repeat(50));
                    }
                }

                // Format successful responses
                const apiResponse = this.formatResponse(response);
                return apiResponse as unknown as AxiosResponse;
            },
            async (error) => {
                // Log error response information if cURL logging is enabled
                if (CURL_LOGGING_CONFIG.enabled && error.response) {
                    const requestStartTime = (error.config as any)?._requestStartTime;
                    const responseTime = requestStartTime ? Date.now() - requestStartTime : 0;
                    console.log(`\n❌ API Error Response [${error.response.status}] - ${responseTime}ms`);
                    console.log(`📍 ${error.config?.method?.toUpperCase()} ${error.config?.url}`);
                    console.log(`💬 ${error.response.statusText}`);
                    console.log('━'.repeat(50));
                }

                // Handle error with proper typing
                return Promise.reject(await this.handleRequestError(error));
            }
        );
    }

    /**
     * Handle request errors, including token refresh for 401 errors
     */
    private async handleRequestError(error: unknown): Promise<ApiResponse> {
        // Early return for non-Axios errors

        if (!axios.isAxiosError(error)) {
            console.log('case 4 ::: ', error)
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error',
                error
            };
        }

        // Now we know error is an AxiosError
        const axiosError = error as AxiosError<unknown, unknown> | any;

        if (axios.isCancel(axiosError)) {
            // Handle cancelled requests
            return {
                success: false,
                message: 'Request was cancelled',
                status: 499, // Client Closed Request
                error: axiosError
            };
        }

        // Safely access config and response properties
        const originalRequest = axiosError.config as CustomRequestConfig | undefined;
        const status = axiosError.response?.status;

        // Handle other errors
        return this.formatError(axiosError);
    }


    private formatError(error: unknown, customMessage?: string): ApiResponse {
        console.log('case 6 ::: ', error)

        // if (error?.code === "ECONNABORTED") {
        //     return {
        //         ...error,
        //         message: i18n.t('errors.http.timeout') as string
        //     };
        // }

        return {
            success: false,
            // message: (error as any)?.response?.data?.message || (error as any)?.message,
            ...(error as any)
        };
    }

    /**
     * Format successful response into standard API response
     */
    private formatResponse(response: AxiosResponse): ApiResponse {
        const data = response.data;

        // If the API already returns in our format, use it directly
        if (data && (typeof data.success === 'boolean')) {
            return {
                ...data,
                status: response.status
            };
        }

        // Otherwise, wrap the response in our standard format
        return {
            success: true,
            data,
            status: response.status,
            message: 'Success'
        };
    }

    /**
     * Set the base URL for API requests and save to storage
     */
    public async setBaseUrl(url: string): Promise<void> {
        try {
            if (!url.startsWith('http')) {
                this.baseURL = `https://${url}`;
            } else {
                this.baseURL = url;
            }

            this.axiosInstance.defaults.baseURL = this.baseURL;

            // Save URL to storage for persistence
            await AsyncStorage.setItem(STORAGE_KEYS.USER_URL, this.baseURL);
            console.log('🔗 Base URL updated and saved:', this.baseURL);
        } catch (error) {
            console.error('Error saving base URL:', error);
        }
    }

    /**
     * Get current base URL
     */
    public getBaseUrl(): string {
        return this.baseURL;
    }

    /**
     * Monitor app state changes to cancel pending requests when app is inactive
     */
    private monitorAppState(): void {
        this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
            if (isIOS && nextAppState === 'background') {
                this.cancelAllRequests('App state changed to ' + nextAppState);
            }
        });
    }

    /**
     * Cancel all pending requests
     */
    public cancelAllRequests(reason: string = 'Operation cancelled'): void {
        pendingRequests.forEach((source) => {
            source.cancel(reason);
        });
        pendingRequests.clear();
    }

    /**
     * Clean up resources
     */
    public cleanup(): void {
        if (this.appStateSubscription) {
            this.appStateSubscription.remove();
        }
        this.cancelAllRequests('API client cleanup');
    }

    /**
     * Make a GET request
     */
    public async get<T = any>(
        endpoint: string,
        params?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.get<T>(endpoint, {
                params,
                ...config,
            });
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            console.log('case 5 ::: get error', error)
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return this.formatError(error as AxiosError<any, any>);
        }
    }

    /**
     * Make a POST request
     */
    public async post<T = any>(
        endpoint: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.post<T>(endpoint, data, config);

            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            // if (error instanceof Error) {
            //     if (axios.isAxiosError(error)) {
            //         console.log('case 1 ::: ', error)
            //         return this.formatError(error as AxiosError<any, any>);
            //     }

            //     console.log('case 2 ::: ', error)
            //     return {
            //         success: false,
            //         message: error.message,
            //         ...(error as any),
            //     };
            // }

            // console.log('case 3 ::: ', error)
            return this.formatError(error as AxiosError<any, any>);
            // return {
            //     ...(error as any),
            //     success: false,
            //     message: 'Unknown error occurred',
            // };
        }
    }

    /**
     * Make a PUT request
     */
    public async put<T = any>(
        endpoint: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.put<T>(endpoint, data, config);
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    /**
     * Make a PATCH request
     */
    public async patch<T = any>(
        endpoint: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.patch<T>(endpoint, data, config);
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    /**
     * Make a DELETE request
     */
    public async delete<T = any>(
        endpoint: string,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        try {
            const response = await this.axiosInstance.delete<T>(endpoint, config);
            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    /**
     * Upload a file with progress tracking
     */
    public async uploadFile<T = any>(
        endpoint: string,
        file: any,
        onProgress?: (percentCompleted: number) => void,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
        const formData = new FormData();

        // If file is an object with uri, add it to FormData
        if (file.uri) {
            const fileType = file.type || 'application/octet-stream';
            const fileName = file.name || file.uri.split('/').pop() || 'file';

            formData.append('file', {
                uri: file.uri,
                type: fileType,
                name: fileName,
            } as any);
        } else if (Array.isArray(file)) {
            // If file is an array, add each file to FormData
            file.forEach((f, index) => {
                const fileType = f.type || 'application/octet-stream';
                const fileName = f.name || f.uri.split('/').pop() || `file${index}`;

                formData.append('files', {
                    uri: f.uri,
                    type: fileType,
                    name: fileName,
                } as any);
            });
        }

        // Add any additional fields to the form data
        if (config?.data) {
            Object.keys(config.data).forEach(key => {
                formData.append(key, config.data[key]);
            });
        }

        try {
            const response = await this.axiosInstance.post<T>(endpoint, formData, {
                ...config,
                headers: {
                    ...config?.headers,
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: (progressEvent) => {
                    if (onProgress && progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        onProgress(percentCompleted);
                    }
                },
            });

            return this.formatResponse(response) as ApiResponse<T>;
        } catch (error) {
            if (error instanceof Error) {
                if (axios.isAxiosError(error)) {
                    return this.formatError(error as AxiosError<any, any>);
                }
                return {
                    success: false,
                    message: error.message,
                    error: error
                };
            }
            return {
                success: false,
                message: 'Unknown error occurred',
                error: error
            };
        }
    }

    private requestToCurl(config: InternalAxiosRequestConfig): string {
        const { method, url, headers, data, baseURL, params } = config;

        // Build full URL
        let fullUrl = (baseURL || '') + (url || '');

        // Add query parameters
        if (params) {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    queryParams.append(key, String(value));
                }
            });
            const queryString = queryParams.toString();
            if (queryString) {
                fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString;
            }
        }

        // Start building cURL command
        let curl = `curl -X ${method?.toUpperCase() || 'GET'}`;

        // Add URL (properly escaped)
        curl += ` '${fullUrl.replace(/'/g, "'\"'\"'")}'`;

        // Add headers (filter out axios internal headers)
        if (headers && CURL_LOGGING_CONFIG.includeHeaders) {
            const excludedHeaders = ['common', 'delete', 'get', 'head', 'post', 'put', 'patch'];

            Object.entries(headers).forEach(([key, value]) => {
                if (!excludedHeaders.includes(key.toLowerCase()) &&
                    value !== undefined &&
                    value !== null &&
                    typeof value === 'string') {
                    // Escape single quotes in header values
                    const escapedValue = String(value).replace(/'/g, "'\"'\"'");
                    curl += ` \\\n  -H '${key}: ${escapedValue}'`;
                }
            });
        }

        // Add request body
        if (data) {
            let dataStr;

            if (typeof data === 'string') {
                dataStr = data;
            } else if (data instanceof FormData) {
                curl += ` \\\n  --form 'data=@[FormData]'`;
                curl += ` \\\n  # Note: FormData content not shown in cURL`;
                return curl;
            } else if (typeof data === 'object') {
                try {
                    dataStr = JSON.stringify(data, null, 0);

                    // Truncate data if too long
                    if (dataStr.length > CURL_LOGGING_CONFIG.maxDataLength) {
                        const truncated = dataStr.substring(0, CURL_LOGGING_CONFIG.maxDataLength);
                        dataStr = truncated + '...[truncated]';
                    }
                } catch (e) {
                    dataStr = '[Complex Object - Cannot stringify]';
                }
            } else {
                dataStr = String(data);
            }

            // Escape single quotes in data
            const escapedData = dataStr.replace(/'/g, "'\"'\"'");
            curl += ` \\\n  -d '${escapedData}'`;
        }

        // Add common cURL options for better debugging
        curl += ` \\\n  --compressed`;
        curl += ` \\\n  --location`;
        curl += ` \\\n  --max-time 30`;

        return curl;
    }

    /**
     * Enable or disable cURL logging
     */
    public setCurlLogging(enabled: boolean): void {
        (CURL_LOGGING_CONFIG as any).enabled = enabled;
    }

    /**
     * Get current cURL logging configuration
     */
    public getCurlLoggingConfig(): typeof CURL_LOGGING_CONFIG {
        return { ...CURL_LOGGING_CONFIG };
    }
}

// Export a singleton instance
export const apiClient = ApiClient.getInstance();

/**
 * Initialize API client with saved URL (call this on app startup)
 */
export const initializeApiClient = async (): Promise<void> => {
    try {
        const savedUrl = await AsyncStorage.getItem(STORAGE_KEYS.USER_URL);
        if (savedUrl) {
            await apiClient.setBaseUrl(savedUrl);
            console.log('🚀 API Client initialized with saved URL:', savedUrl);
        } else {
            console.log('🚀 API Client initialized with default URL');
        }
    } catch (error) {
        console.error('Error initializing API client:', error);
    }
};

// Export utility functions for cURL logging
export const curlLogger = {
    /**
     * Enable cURL logging
     */
    enable: () => apiClient.setCurlLogging(true),

    /**
     * Disable cURL logging
     */
    disable: () => apiClient.setCurlLogging(false),

    /**
     * Get current configuration
     */
    getConfig: () => apiClient.getCurlLoggingConfig(),

    /**
     * Toggle cURL logging
     */
    toggle: () => {
        const config = apiClient.getCurlLoggingConfig();
        apiClient.setCurlLogging(!config.enabled);
        return !config.enabled;
    }
};

// Export a hook for using the API client in components
export const useApi = () => {
    const toast = useToast();

    const handleApiError = (error: any) => {
        if (error && error.message) {
            toast.showToast(error.message, 'error');
        } else {
            toast.showToast('toast.networkError', 'error');
        }
    };

    return {
        apiClient,
        handleApiError,
        curlLogger, // Include cURL logger utilities
    };
};

export default apiClient;