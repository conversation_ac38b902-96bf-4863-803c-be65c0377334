import { StyleSheet, ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { AppTheme } from '@/types';
import { COLORS, FONT_FAMILY, FONT_SIZE, SCALE, SPACING } from '@/constants/UI/themes';
import {
    isAndroid,
    adjustSpacing,
    getResponsiveFontSize
} from '@/constants/UI/responsive';

export const createAuthStyles = (theme: AppTheme) => {
    const { colors } = theme;

    return StyleSheet.create({
        container: {
            flex: 1,
        } as ViewStyle,
        contentContainer: {
            flex: 1,
            justifyContent: 'center',
            // paddingHorizontal: 20,
        },
        scrollContainer: {
            flexGrow: 1,
            justifyContent: 'center',
            minHeight: '100%',
        } as ViewStyle,
        logoContainer: {
            alignItems: 'center',
            marginBottom: adjustSpacing(isAndroid ? SPACING.md : SPACING.xl),
            marginTop: adjustSpacing(isAndroid ? SPACING.xl : SPACING.xl * 2),
        } as ViewStyle,
        logo: {
            width: adjustSpacing(251),
            height: adjustSpacing(159),
            resizeMode: 'contain',
            ...(isAndroid && { transform: [{ scale: SCALE }] }),
        } as ImageStyle,
        formContainer: {
            width: '100%',
            paddingHorizontal: adjustSpacing(SPACING.lg),
            flexGrow: 1,
            paddingBottom: adjustSpacing(50),
            justifyContent: 'space-between',
        } as ViewStyle,
        fieldContainer: {
            marginBottom: adjustSpacing(isAndroid ? SPACING.md : SPACING.xl),
        } as ViewStyle,
        labelContainer: {
            flexDirection: 'row',
            marginBottom: adjustSpacing(SPACING.xs),
        } as ViewStyle,
        label: {
            color: colors.gray,
            fontSize: FONT_SIZE.lg,
            fontFamily: FONT_FAMILY.regular,
        } as TextStyle,
        required: {
            color: colors.error,
            marginLeft: 4,
        } as TextStyle,
        urlContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: colors.surface,
            borderRadius: theme.sizes.borderRadius.small,
            height: isAndroid ? theme.sizes.inputHeight * SCALE : theme.sizes.inputHeight,
        } as ViewStyle,
        urlPrefix: {
            ...theme.fonts.bodyLarge,
            color: colors.text,
            paddingLeft: SPACING.sm,
            opacity: 0.5,
            fontSize: getResponsiveFontSize(theme.fonts.bodyLarge.fontSize),
        } as TextStyle,
        urlInput: {
            flex: 1,
            backgroundColor: 'transparent',
            paddingVertical: adjustSpacing(SPACING.xs),
            paddingHorizontal: adjustSpacing(SPACING.sm),
            ...theme.fonts.bodyLarge,
            fontSize: getResponsiveFontSize(theme.fonts.bodyLarge.fontSize),
            marginLeft: -8,
            color: colors.gray,
        } as TextStyle,
        input: {
            backgroundColor: colors.surface,
            borderRadius: theme.sizes.borderRadius.small,
            height: isAndroid ? theme.sizes.inputHeight * SCALE : theme.sizes.inputHeight,
            paddingVertical: adjustSpacing(SPACING.xs),
            paddingHorizontal: adjustSpacing(SPACING.sm),
            ...theme.fonts.bodyLarge,
            fontSize: getResponsiveFontSize(theme.fonts.bodyLarge.fontSize),
            width: '100%',
            color: colors.gray,
        } as TextStyle,
        error: {
            ...theme.fonts.bodyMedium,
            color: colors.error,
            marginTop: isAndroid ? 2 : adjustSpacing(SPACING.xs),
            ...(isAndroid && {
                fontSize: getResponsiveFontSize(theme.fonts.bodyMedium.fontSize * 0.8),
                lineHeight: theme.fonts.bodyMedium.fontSize * SCALE
            }),
        } as TextStyle,
        actionButton: {
            backgroundColor: colors.primary,
            borderRadius: theme.sizes.borderRadius.medium,
            height: isAndroid ? theme.sizes.buttonHeight * SCALE : theme.sizes.buttonHeight,
            width: '100%',
            alignSelf: 'center',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: adjustSpacing(isAndroid ? SPACING.md : SPACING.xl),
        } as ViewStyle,
        actionButtonText: {
            ...theme.fonts.titleMedium,
            color: colors.onPrimary,
            fontSize: getResponsiveFontSize(theme.fonts.titleMedium.fontSize),
        } as TextStyle,
        linkButton: {
            marginTop: adjustSpacing(isAndroid ? SPACING.xs : SPACING.md),
            marginBottom: adjustSpacing(isAndroid ? SPACING.md : 0),
            minHeight: 30,
            paddingVertical: 5,
        } as ViewStyle,
        linkButtonText: {
            ...theme.fonts.titleMedium,
            color: colors.price,
            textAlign: 'center',
            fontSize: getResponsiveFontSize(theme.fonts.titleMedium.fontSize),
        } as TextStyle,
        checkboxContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: adjustSpacing(isAndroid ? SPACING.sm : SPACING.lg),
        } as ViewStyle,
        passwordContainer: {
            position: 'relative',
        },
        eyeIcon: {
            position: 'absolute',
            right: 15,
            top: 12,
            zIndex: 1,
        },
    });
};