import { COLORS } from "@/constants/UI/themes";
import { StyleSheet } from "react-native";


export const createCameraTakePhotoScreenStyles = () => StyleSheet.create({
    container: { flex: 1, backgroundColor: COLORS["light"].background },
    controlContainer: {
        position: 'absolute',
        bottom: 20,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        marginBottom: 50
    },
    flashContainer: {
        position: 'absolute',
        top: 30,
        right: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
    },
    captureButton: {
        width: 70,
        height: 70,
        borderRadius: 35,
        borderWidth: 5,
        borderColor: COLORS["light"].border,
        justifyContent: 'center',
        alignItems: 'center',
    },
    captureInner: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: COLORS["light"].background,
    },
    iconButton: {
        padding: 10,
    },
    secondaryControls: {
        position: 'absolute',
        bottom: 100,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingHorizontal: 20,
    },
    secondaryButton: {
        backgroundColor: COLORS["light"].secondary,
        paddingVertical: 8,
        paddingHorizontal: 15,
        borderRadius: 20,
    },
    button: {
        backgroundColor: COLORS["light"].primary,
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 5,
        marginTop: 10,
    },
    buttonText: {
        color: COLORS["light"].primary,
        fontSize: 16,
        textAlign: 'center',
    },
    actionButton: {
        backgroundColor: COLORS["light"].primary,
        paddingVertical: 5,
        paddingHorizontal: 10,
        borderRadius: 5,
        marginHorizontal: 5,
    },
    actionText: {
        color: COLORS["light"].primary,
        fontWeight: '700',
        fontSize: 12,
    },
    functionContainer: {
        width: '30%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    thumbnail: {
        width: 50,
        height: 50,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS["light"].border,
    },
    modalContent: {
        backgroundColor: COLORS["light"].background,
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        paddingBottom: 20,
        width: "100%"
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: COLORS["light"].border,
    },
    modalTitle: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    modalError: {
        padding: 20,
        alignItems: 'center',
    },
    photoContainer: {
        margin: 5,
        alignItems: 'center',
    },
    photo: {
        width: 100,
        height: 100,
        borderRadius: 5,
    },
    photoActions: {
        flexDirection: 'row',
        marginTop: 5,
    },
    selectedOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: COLORS["light"].primary,
        opacity: 0.5,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkmark: {
        color: 'white',
        fontSize: 24,
        fontWeight: 'bold',
    },
    focusPoint: {
        position: 'absolute',
        width: 50,
        height: 50,
        borderRadius: 25,
        borderWidth: 2,
        borderColor: COLORS["light"].primary,
        backgroundColor: COLORS["light"].border,
        opacity: 0.5
    },
    errorText: {
        color: COLORS["light"].error,
        textAlign: 'center',
        marginTop: 20,
        fontSize: 16,
    },
    imageViewerModal: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: COLORS["light"].border,
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageViewerContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageViewer: {
        width: '100%',
        height: '80%',
    },
    closeButton: {
        position: 'absolute',
        top: 40,
        right: 20,
        backgroundColor: COLORS["light"].primary,
        padding: 10,
        borderRadius: 5,
    },
    headerActions: {
        flexDirection: 'row',
        gap: 20,
    },
    deleteButton: {
        color: COLORS["light"].error,
    },
    activeButton: {
        color: COLORS["light"].primary,
    },
    disabledButton: {
        color: COLORS["light"].disabled,
    },
    zoomTextContainer: {
        position: 'absolute',
        top: 50,
        alignSelf: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        padding: 8,
        borderRadius: 8,
    },
    barcodeTextContainer: {
        position: 'absolute',
        top: 100,
        alignSelf: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        padding: 8,
        borderRadius: 8,
    },
    zoomText: {
        color: 'white',
        fontSize: 16,
    },
    corner: {
        width: 20,
        height: 20,
        borderColor: 'lime',
        borderLeftWidth: 4,
        borderTopWidth: 4,
        position: 'absolute',
    },
});