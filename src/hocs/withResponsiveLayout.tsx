import React from 'react';
import { StyleSheet, ViewStyle, StyleProp, Keyboard, TouchableWithoutFeedback, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ResponsiveContainer from '../components/ResponsiveContainer';
import { BASE_STYLE } from '@/constants/UI/responsiveStyles';
import { GRADIENT_VALUE } from '@/constants/UI/themes';
import { LinearGradient } from 'react-native-linear-gradient'
import { useTheme } from '@react-navigation/native';
import { ThemeColors } from '@/types';

interface WithResponsiveLayoutProps {
    containerStyle?: StyleProp<ViewStyle>;
    fluid?: boolean;
    ignoreBottomInset?: boolean;
    keyboardAvoidingEnabled?: boolean;
    linearGradient?: boolean;
}

const withResponsiveLayout = <P extends object>(
    WrappedComponent: React.ComponentType<P>
) => {
    const WithResponsiveLayoutComponent: React.FC<P & WithResponsiveLayoutProps> = ({
        containerStyle,
        fluid,
        ignoreBottomInset = true,
        keyboardAvoidingEnabled = true,
        linearGradient = false,
        ...props
    }) => {
        const { colors } = useTheme();

        const renderContent = () => (
            <ResponsiveContainer
                style={containerStyle}
                fluid={fluid}
                ignoreBottomInset={ignoreBottomInset}
            >
                <WrappedComponent {...(props as P)} />
            </ResponsiveContainer>
        );

        if (!keyboardAvoidingEnabled) {
            return (
                <SafeAreaView
                    style={[styles.safeArea]}
                    edges={['top', 'left', 'right']}
                >
                    {renderContent()}
                </SafeAreaView>
            );
        }

        if (linearGradient) {
            return (
                <View style={styles.safeArea}>
                    <LinearGradient
                        colors={(colors as ThemeColors).linearGradient}
                        locations={GRADIENT_VALUE}
                        style={styles.safeArea}
                    >
                        <SafeAreaView
                            style={[styles.safeArea]}
                            edges={['left', 'right']}
                        >
                            {renderContent()}
                        </SafeAreaView>
                    </LinearGradient>
                </View>
            );
        }

        return (
            <View style={styles.safeArea}>
                <SafeAreaView
                    style={[styles.safeArea]}
                    edges={['left', 'right']}
                >
                    {renderContent()}
                </SafeAreaView>
            </View>
        );
    };

    WithResponsiveLayoutComponent.displayName = `WithResponsiveLayout(${
        WrappedComponent.displayName || WrappedComponent.name || 'Component'
    })`;

    return WithResponsiveLayoutComponent;
};

const styles = StyleSheet.create({
    safeArea: {
        flex: BASE_STYLE.flex.flex,
        backgroundColor: 'transparent',
    },
});

export default withResponsiveLayout;