export default {
    common: {
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        cancel: 'Cancel',
        confirm: 'Confirm',
        logout: 'Logout',
        save: 'Save',
        delete: 'Delete',
        edit: 'Edit',
        close: 'Close',
        back: 'Back',
        done: 'Done',
        next: 'Next',
        previous: 'Previous',
        search: 'Search',
        filter: 'Filter',
        sort: 'Sort',
        apply: 'Apply',
        reset: 'Reset',
        clear: 'Clear',
        select: 'Select',
        selectAll: 'Select All',
        unselectAll: 'Unselect All',
        selectItem: 'Select {{item}}',
        unselectItem: 'Unselect {{item}}',
        updating: 'Updating...',
        update: 'Update Password',
        openSettings: 'Open Settings'
    },
    weekDays: {
        mon: 'Mon',
        tue: 'Tue',
        wed: 'Wed',
        thu: 'Thu',
        fri: 'Fri',
        sat: 'Sat',
        sun: 'Sun',
    },
    dashboard: {
        tasks: "{{count}} Jobs",
    },
    auth: {
        login: 'Login',
        signin: 'Sign In',
        logout: 'Logout',
        forgotPassword: 'Forgot Password',
        rememberMe: 'Remember Me',
        requestPasswordReset: 'Request Password Reset',
        email: 'Email',
        password: 'Password',
        signUp: 'Sign Up',
        logoutConfirmation: 'Are you sure you want to logout?',
        logoutSuccess: 'You have been logged out successfully',
        logoutFailed: 'Logout failed',
        sessionExpired: 'Your session has expired, please log in again',
        loginSuccess: 'Login successfully',
        tokenRefreshed: 'Session refreshed successfully',
        passwordResetSent: 'Password reset instructions have been sent to your email',
        passwordChanged: 'Password changed successfully',
        passwordChangeFailed: 'Failed to change password'
    },
    menu: {
        home: 'Home',
        batch: 'Batch',
        report: 'Report and History',
        customer: 'Customer',
        inventory: 'Inventory',
        settings: 'Settings',
        language: 'Language',
        selectLanguage: 'Select Language',
        demo: 'Demo'
    },
    screens: {
        dashboard: 'Dashboard',
        demo: 'Demo',
        login: 'Login',
        forgotPassword: 'Forgot Password',
        changePassword: {
            title: "Change Password"
        },
        detailTask: 'Detail Task',
        settings: 'Settings',
        profile: 'Profile',
        mapDemo: 'Map Demo',
        generalSetting: 'General Setting',
        batch: 'Batch',
        customer: 'Customer',
        inventory: 'Inventory',
        report: 'Report and History',
    },
    errors: {
        networkError: 'Network error',
        unauthorized: 'Unauthorized access',
        logoutError: 'Error logging out',
        tokenExpired: 'Your session has expired',
        invalidCredentials: 'Invalid email or password',
        invalidToken: 'Invalid token format',
        userNotFound: 'User not found',
        emailNotFound: 'Email not found in the system',
        loginFailed: 'Email or password is incorrect',
        somethingWentWrong: 'Something went wrong, please try again',
        updateChangePasswordFailed: 'Failed to update password. Please try again',
        http: {
            400: 'Bad request',
            401: 'Unauthorized access',
            403: 'Forbidden access',
            404: 'Not found',
            422: 'Unprocessable entity',
            500: 'Internal server error',
            default: 'An unexpected error occurred',
            "timeout": "Login failed. Please check your URL, email and password",
            "timeoutForgotPasswod": "Reset password failed. Please check your URL, email",
            "incorrectUrl": "Invalid URL. Please check the Website URL and try again",

        }
    },
    demo: {
        cards: {
            basic: {
                title: 'Basic Card',
                description: 'This is a basic card that adapts to both mobile and tablet layouts. Try rotating your device!'
            },
            custom: {
                title: 'Custom Styled Card',
                description: 'This card has custom styling applied to it.'
            },
            theme: {
                light: 'Light Theme',
                dark: 'Dark Theme'
            }
        },
        inputs: {
            email: {
                label: 'Email',
                placeholder: 'Enter email'
            },
            phone: {
                label: 'Phone Number',
                placeholder: 'Enter phone number'
            }
        }
    },
    fields: {
        url: {
            label: 'URL',
            placeholder: 'name.avb.com',
        },
        email: {
            label: 'Email',
            placeholder: '<EMAIL>',
        },
        password: {
            label: 'Password',
            placeholder: 'Enter password',
        },
        currentPassword: {
            label: "Current Password",
            placeholder: "Enter current password"
        },
        newPassword: {
            label: "New Password",
            placeholder: "Enter new password"
        },
        confirmPassword: {
            label: "Confirm Password",
            placeholder: "Re-enter new password"
        }
    },
    validation: {
        required: '{{field}} is required',
        email: {
            invalid: 'Please enter a valid email address',
        },
        url: {
            invalid: 'Must match a valid URL pattern',
        },
        password: {
            tooShort: 'Password must be at least {{count}} characters',
            complexity: 'Password must include uppercase, lowercase, number, and special character',
            minLength: 'Password must be at least 12 characters',
            notSameAsCurrent: 'New password must be different from current password',
            mustMatch: 'Confirm password does not match',
            passwordIncorrect: 'Password is incorrect'
        },
    },
    roles: {
        technician: 'Technician',
        manager: 'Manager',
        admin: 'Administrator',
        user: 'User'
    },
    settings: {
        generalSettings: "General Settings",
        pushNotification: "Push Notification",
        allowLocation: "Allow Location",
        revokeNotificationTitle: "Revoke Notification Permission",
        revokeNotificationMessage: "You will be redirected to settings to revoke notification permission for this app.",
        revokeLocationTitle: "Revoke Location Permission",
        revokeLocationMessage: "You will be redirected to settings to revoke location permission for this app.",
        permissionWarning: "You haven't allowed notifications/location from this app",
        version: "Version {{version}}",
        termsOfUse: "Terms of Use",
        permissionNeededTitle: "Permission Needed",
        permissionNeededMessage: "Please open settings to grant the requested permission",
        privacyPolicy: "Privacy Policy",
        contactAdmin: "Contact Support",
        emailNotSupported: "Email Not Supported",
        emailClientError: "Your device doesn't have an email client configured.",
        emailError: "Error",
        emailErrorMessage: "Could not open email client. Please try again later."
    },
    toast: {
        permissionGranted: "Permission granted",
        permissionDenied: "Permission denied",
        operationSuccess: "Operation completed successfully",
        operationError: "Operation failed",
        networkError: "Network connection error",
        settingsOpenError: "Could not open device settings",
        dataUpdateSuccess: "Data updated successfully",
        dataUpdateError: "Failed to update data"
    },
    "Email or password is incorrect": "Email or password is incorrect"
}; 