import React, { useLayoutEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { StatusBar } from "react-native";

import { STATUS_BAR, THEME } from "@/constants/UI/themes";
import { ThemeOptions } from "@/types";
import { selectTheme } from "@/redux/slices/themeSlice";
import { AppNavigator } from './AppNavigator';
import { AuthNavigator } from './AuthNavigator';
import { checkSession } from '@/redux/slices/authSlice';
import { AppDispatch } from '@/redux/store';

function RootNavigator(): React.JSX.Element {
    const isDrawerOpen = useRef(false);
    const themeMode: ThemeOptions = useSelector(selectTheme);
    const { isAuthenticated, loading } = useSelector((state: any) => state.auth);
    const dispatch = useDispatch<AppDispatch>();


    useLayoutEffect(() => {
        dispatch(checkSession());
    }, [dispatch]);

    const renderContent = () => {
        if (loading && !isAuthenticated) return <></>;
        return isAuthenticated ? <AppNavigator /> : <AuthNavigator />;
    };

    return (
        <>
            <StatusBar
                barStyle={themeMode === THEME.DARK ? STATUS_BAR.LIGHT : STATUS_BAR.DARK}
                hidden={isDrawerOpen.current}
            />
            {renderContent()}
        </>
    );
}

export default RootNavigator;