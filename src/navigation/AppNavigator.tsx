import { ScreenConfig } from "@/types";
import { createDrawerNavigator, DrawerNavigationOptions } from "@react-navigation/drawer";
import CustomDrawer from "@/components/CustomDrawer";
import { useTheme } from "@react-navigation/native";
import { COLORS, FONT_FAMILY, FONT_SIZE, FONT_WEIGHT } from "@/constants/UI/themes";
import { Dimensions, StatusBar, Text, TouchableOpacity, ViewStyle } from "react-native";
import { useCallback, useEffect, useRef, useState } from "react";
import { NAVIGATOR_TYPES, getScreenConfigsByType } from "@/constants/navigation";
import { BottomSheetProvider } from "@/hocs/BottomSheetContext";
import PermissionsHandler from "@/components/PermissionsHandler";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { getUserInfo } from "@/redux/slices/userSlice";
import CIcon from "@/components/CIcon";
import { I<PERSON>, <PERSON><PERSON>noti } from "@/constants/UI/icons";
import NotificationScreen from '@/screens/NotificationScreen';
import { AppTheme } from '@/types';
const { colors } = useTheme() as unknown as AppTheme;

export function AppNavigator() {

    const [showNotifications, setShowNotifications] = useState(false);
    const Drawer = createDrawerNavigator();
    const { width } = Dimensions.get('window');
    const isDrawerOpen = useRef(false);
    const dispatch = useDispatch<AppDispatch>();
    
    const screenList: ScreenConfig[] = getScreenConfigsByType(NAVIGATOR_TYPES.DRAWER);

    const renderScreenList = useCallback(() => {
        return screenList.map((screen, index) => (
            <Drawer.Screen
                key={index}
                name={screen.name}
                component={screen.component}
                options={screen.options as DrawerNavigationOptions}
            />
        ));
    }, [screenList]);

    const handlePermissionsGranted = useCallback(() => {
        dispatch(getUserInfo()).unwrap()
            .then((result) => {
            })
            .catch((error) => {
                console.log('fetchUserInfo error', error);
            });
    }, []);

    return (
        <PermissionsHandler
            requiredPermissions={['notification', 'location']}
            optionalPermissions={['camera', 'storage']}
            autoRequestRequired={true}
            autoRequestOptional={false}
            onPermissionsGranted={handlePermissionsGranted}
        >
            <BottomSheetProvider>
                <Drawer.Navigator
                    initialRouteName={screenList[0].name}
                    drawerContent={(props) => <CustomDrawer {...props} />}
                    screenOptions={({ navigation, route }) => {
                        const isHome = route.name === screenList[0].name;
                        return {
                            headerStyle: !isHome
                            ? {
                                backgroundColor: colors.green,
                                elevation: 0,
                            }
                            : {
                                backgroundColor: colors.background,
                            },
                            headerTintColor: colors.text,
                            headerTitleStyle: {
                                fontFamily: FONT_FAMILY.bold,
                                fontWeight: FONT_WEIGHT.semiBold,
                                textAlign: 'center',
                                alignSelf: 'center',
                                flex: 1,
                                paddingTop: 15,
                                fontSize: FONT_SIZE.size26,
                            },
                            headerTitleAlign: !isHome ? 'center' : 'left' ,
                            headerLeft: !isHome
                                ? () => (
                                    <TouchableOpacity
                                        style={{ marginLeft: 16 }}
                                        onPress={() => navigation.goBack()}
                                    >
                                        <CIcon source={Iback} size={30} />
                                    </TouchableOpacity>
                                )
                                : undefined,
                            headerRight: !isHome
                                ? undefined
                                : () => (
                                    <TouchableOpacity onPress={() => navigation.navigate('Notifications')} style={{ marginRight: 16 }}>
                                        <CIcon source={Ibellnoti} size={30} />
                                    </TouchableOpacity>
                                ),
                            drawerStyle: {
                                width: width,
                                backgroundColor: colors.background,
                            } as ViewStyle,
                            drawerType: 'front',
                            overlayColor: 'rgba(0,0,0,0.7)',
                            swipeEdgeWidth: 100,
                            drawerLabelStyle: {
                                fontFamily: FONT_FAMILY.regular,
                                marginLeft: -16,
                            },
                            drawerActiveTintColor: colors.primary,
                            drawerInactiveTintColor: colors.text,
                        };
                    }}
                    screenListeners={({ navigation }) => ({
                        state: (e) => {
                            const navState = navigation.getState();
                            const isOpen = navState.history
                                ? navState.history.some(h => h.type === 'drawer')
                                : false;
                            isDrawerOpen.current = isOpen;
                            StatusBar.setHidden(isOpen);
                        }
                    })}
                >

                    {renderScreenList()}
                    <Drawer.Screen
                        name="Notifications"
                        component={NotificationScreen}
                    />

                </Drawer.Navigator>
            </BottomSheetProvider>

        </PermissionsHandler>

    );
}