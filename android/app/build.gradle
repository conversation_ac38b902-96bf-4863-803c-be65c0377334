apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'
// Add
// C<PERSON>u hình react-native-config cho multiple environments
project.ext.envConfigFiles = [
    debug: ".env",
    release: ".env.production",
    staging: ".env.staging"
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
   

    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    // namespace project.env.get("APP_ID") 
    namespace "net.avb.arc" 

    defaultConfig {
        applicationId project.env.get("APP_ID")    
        minSdkVersion rootProject.ext.minSdkVersion
            targetSdkVersion rootProject.ext.targetSdkVersion
            versionCode 25061101
            versionName "1.0.0"
            resValue "string", "build_config_package", project.env.get("APP_ID") 

        resValue "string", "api_url", project.env.get("API_URL")
        def apiUrl = project.env.get("API_URL")
        if (apiUrl) {
            def apiDomain = new URL(apiUrl).getHost()
            resValue "string", "api_domain", apiDomain
        }
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        staging {
            storeFile file(project.env.get("KEYSTORE_FILE") ?: 'avb-mobile-app.keystore')
            storePassword project.env.get("KEYSTORE_PASSWORD")
            keyAlias project.env.get("KEY_ALIAS")
            keyPassword project.env.get("KEY_PASSWORD")
        }
        release {
            storeFile file(project.env.get("KEYSTORE_FILE") ?: 'avb-mobile-app.keystore')
            storePassword project.env.get("KEYSTORE_PASSWORD")
            keyAlias project.env.get("KEY_ALIAS")
            keyPassword project.env.get("KEY_PASSWORD")
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            // applicationIdSuffix ".debug" // Removed to use fixed package name
            resValue "string", "app_name",project.env.get("APP_NAME")
            resValue "string", "api_url", project.env.get("API_URL")

        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            resValue "string", "app_name", project.env.get("APP_NAME") 
            debuggable false // disable if release app            
            resValue "string", "api_url", project.env.get("API_URL")

        }
        staging {
            initWith debug
            // applicationIdSuffix ".staging" // Removed to use fixed package name
            matchingFallbacks = ['debug']
            resValue "string", "app_name", project.env.get("APP_NAME")
            resValue "string", "api_url", project.env.get("API_URL")
    }
}
}


dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    // Add
    implementation project(':react-native-permissions')
    implementation project(':react-native-config')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}